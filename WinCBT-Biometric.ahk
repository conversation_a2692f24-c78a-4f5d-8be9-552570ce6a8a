#Requires Autohotkey v2

#SingleInstance Force
; Include error handler first so it's available to all other modules
#Include %A_ScriptDir%\lib\error_handler.ahk

; Include path manager first so it's available to all other modules
#Include %A_ScriptDir%\lib\PathManager.ahk

; Include core functionality modules
#Include %A_ScriptDir%\lib\biometric_functions.ahk
#Include %A_ScriptDir%\lib\db_functions.ahk
#Include %A_ScriptDir%\lib\webcam_utils.ahk
#Include %A_ScriptDir%\lib\secugen_wrapper.ahk
#Include %A_ScriptDir%\lib\post_exam_utils.ahk

; Include UI and utility modules
#Include %A_ScriptDir%\lib\settings.ahk

; Application version and build information
global version := "(v1.5.8 Build 20250525)"
global APP_NAME := "WinCBT-Biometric"

; Global manager instances
global g_dbManager := ""          ; Database manager
global g_fingerprintManager := "" ; Fingerprint manager
global myGui := ""                ; Main GUI

; Global webcam variables
global webcamControl := ""        ; Control for live webcam feed
global capturedImageControl := "" ; Control for displaying captured image
global capHwnd := 0               ; Webcam capture window handle
global isWebcamActive := false    ; Flag to track if webcam is active
global cameraName := ""           ; Currently selected camera name
global dismissDialogsTimer := 0   ; Timer for dismissing webcam dialogs
global hModule := 0               ; Handle to the avicap32.dll library

; Load the Video for Windows library
hModule := DllCall("LoadLibrary", "Str", "avicap32.dll")
if (!hModule) {
    ErrorHandler.LogMessage("WARNING", "Failed to load avicap32.dll. This is required for webcam functionality.")
}

; Global variables for GUI controls - these will be assigned in the Constructor function
global SingleThumbCheckbox
global LeftThumbRadio
global RightThumbRadio
global SpecialCaseIndicator
global ButtonCaptureFingerprint
global ButtonCaptureRightFingerprint
global FingerprintStatusValue
global RightFingerprintStatusValue
global sbMain := ""               ; Status bar

; Search and candidate info controls
global EditRollSearch, ButtonSearch, EditNameSearch
global CandidateNameText, FatherNameText, GenderText, DateOfBirthText, LanguageText, SpecialStatusText
global RegisteredPhoto, RegisteredSignature, SignatureCaptureImage

; Capture controls
global ReviewPhotoLabel, ButtonCapturePicture, ButtonRecapturePhoto, ButtonUsePhoto
global ButtonCaptureSignature, EnableRightThumbCheckbox, WebcamFeed
global CaptureGroupBox            ; Capture section group box

; Verification controls
global VerifyPhotoImage, PhotoStatusValue, ButtonVerifyPicture
global VerifyFingerprintImage, ButtonVerifyFingerprint
global VerifyRightFingerprintImage, ButtonVerifyRightFingerprint
global SignatureVerifyImage, SignatureStatusValue, ButtonVerifySignature
global VerificationStatusValue, PostVerificationStatusValue, AssignedSeatValue, SeatDetailsText, ButtonAssignSeat
global VerificationGroupBox       ; Verification section group box

; Application state variables
global g_isPostExamMode := false  ; Flag to track post-exam mode for current candidate
global g_invalidPostExam := false ; Flag to track invalid post-exam verification attempts
global g_specialDialogShown := false ; Flag to track if special candidate dialog has been shown
global g_fingerprintReaderStatus := "Not Connected" ; Status of fingerprint reader
global g_fingerprintDeviceSN := ""                  ; Serial number of fingerprint reader
global g_cameraStatus := "Not Connected"            ; Status of camera

; Global variable for special candidate accommodation selection
global g_specialAccommodation := {leftThumb: false, rightThumb: false, isSpecialCandidate: false}

; Camera state management variables
global g_cameraInitialized := false ; Flag to track if camera has been initialized
global g_cameraVerificationDone := false ; Flag to track if startup verification is complete
global g_validCandidateLoaded := false ; Flag to track if a valid candidate is currently loaded

; Fingerprint capture state management variables
global g_leftFingerprintCapturing := false ; Flag to track left fingerprint capture in progress
global g_rightFingerprintCapturing := false ; Flag to track right fingerprint capture in progress

; Initialize the error handler first
; Set debug mode based on command line parameter or config setting
debugMode := false
for n, param in A_Args {
    if (param = "/debug" || param = "-debug") {
        debugMode := true
        break
    }
}

; Try to read debug mode from config if not set by command line
if (!debugMode) {
    try {
        ; First try the new config file
        if (FileExist(A_ScriptDir "\WinCBT-Biometric.ini")) {
            debugMode := IniRead(A_ScriptDir "\WinCBT-Biometric.ini", "Settings", "DebugMode", "0") = "1"
        }
        ; No fallback needed - use WinCBT-Biometric.ini only
    } catch {
        ; Ignore errors reading config
    }
}

ErrorHandler.Initialize(StatusCallback, debugMode)
ErrorHandler.LogMessage("INFO", "Starting " APP_NAME " " version)
if (debugMode)
    ErrorHandler.LogMessage("INFO", "Debug mode enabled")

; Initialize the PathManager
try {
    PathManager.Initialize()
    ErrorHandler.LogMessage("INFO", "PathManager initialized successfully")
} catch as err {
    ErrorHandler.LogMessage("CRITICAL", "Failed to initialize PathManager: " err.Message)
    ErrorHandler.ShowError("Failed to initialize PathManager: " err.Message,
                          APP_NAME " - Critical Error", "Icon! 262144")
}

; Validate required files and directories
ErrorHandler.ValidateRequiredFiles()

; Initialize the GUI
myGui := Constructor()

; Add OnExit handler for proper cleanup
CloseFunc(ExitReason, ExitCode) {
    global capHwnd, dismissDialogsTimer, g_fingerprintManager, hModule

    ; Log application exit
    ErrorHandler.LogMessage("INFO", "Application exiting: " ExitReason " (Code: " ExitCode ")")

    ; Stop dialog dismissal timer if active
    try {
        if (dismissDialogsTimer) {
            SetTimer(dismissDialogsTimer, 0)
            dismissDialogsTimer := 0
        }
    } catch {
        ; Ignore errors
    }

    ; Stop webcam if active
    if (capHwnd) {
        try {
            StopWebcam(capHwnd)
            capHwnd := 0
        } catch as err {
            ErrorHandler.LogMessage("WARNING", "Error stopping webcam: " err.Message)
        }
    }

    ; Unload the avicap32.dll library if loaded
    if (hModule) {
        try {
            DllCall("FreeLibrary", "Ptr", hModule)
            hModule := 0
            ErrorHandler.LogMessage("INFO", "Unloaded avicap32.dll library")
        } catch as err {
            ErrorHandler.LogMessage("WARNING", "Error unloading avicap32.dll: " err.Message)
        }
    }

    ; Close fingerprint manager if active
    if (IsObject(g_fingerprintManager))
        g_fingerprintManager.Close()
}
OnExit(CloseFunc)
myGui.OnEvent("Close", (*) => ExitApp())

; Status callback function for error handler
StatusCallback(message) {
    SafeUpdateStatusBar(message)
}

; Helper function to safely update status bar with consistent formatting
SafeUpdateStatusBar(message) {
    global sbMain
    if (IsSet(sbMain) && IsObject(sbMain)) {
        try {
            sbMain.Text := message
            OutputDebug("Status: " message)
        } catch as err {
            OutputDebug("Error updating status bar: " err.Message)
        }
    } else {
        OutputDebug("Status (no status bar): " message)
    }
}

; ; === CAMERA STATE MANAGEMENT ===

; ; SetCameraInactiveState()
; ; Sets the camera to a uniform inactive state with consistent "No Live Camera Feed" message
; ; This replaces all placeholder images and inconsistent inactive states
SetCameraInactiveState() {
    OutputDebug("SetCameraInactiveState: Setting uniform inactive camera state")

    try {
        global capHwnd, webcamControl, capturedImageControl, isWebcamActive
        global g_cameraStatus

        ; Stop any active camera feed
        if (capHwnd && isWebcamActive) {
            StopWebcam(capHwnd)
            capHwnd := 0
            isWebcamActive := false
            OutputDebug("SetCameraInactiveState: Stopped active camera feed")
        }

        ; Set uniform inactive state
        if (IsObject(webcamControl) && IsObject(capturedImageControl)) {
            ; Hide the captured image control
            HideControl(capturedImageControl)

            ; Show the webcam control with inactive message
            ShowControl(webcamControl)

            ; Set consistent inactive message
            if (webcamControl.HasProp("Text")) {
                webcamControl.Text := "No Live Camera Feed"
            }

            OutputDebug("SetCameraInactiveState: Camera controls set to uniform inactive state")
        }

        ; Update camera status
        g_cameraStatus := "Inactive"

        return true
    } catch as err {
        OutputDebug("SetCameraInactiveState: Error setting inactive state: " err.Message)
        return false
    }
}

; ; SetCameraActiveState()
; ; Activates the camera for valid candidates and shows live feed
; ; @param candidateStatus: The status of the candidate (for validation)
; ; @return: true if camera activated successfully, false otherwise
SetCameraActiveState(candidateStatus := "") {
    OutputDebug("SetCameraActiveState: Activating camera for valid candidate")

    try {
        global capHwnd, webcamControl, capturedImageControl, cameraName, isWebcamActive
        global g_cameraStatus, g_cameraInitialized

        ; Validate that camera should be active for this candidate
        if (candidateStatus == "Seat already assigned" || candidateStatus == "Not Active") {
            OutputDebug("SetCameraActiveState: Camera should not be active for candidate status: " candidateStatus)
            return SetCameraInactiveState()
        }

        ; Initialize camera if not already done
        if (!g_cameraInitialized) {
            if (!InitializeCameraOnStartup()) {
                OutputDebug("SetCameraActiveState: Failed to initialize camera")
                return SetCameraInactiveState()
            }
        }

        ; Start camera if not already active
        if (!capHwnd || !isWebcamActive) {
            capHwnd := StartWebcam(webcamControl, cameraName)
            if (capHwnd) {
                isWebcamActive := true
                g_cameraStatus := cameraName ? cameraName : "Connected"
                OutputDebug("SetCameraActiveState: Camera started successfully")
            } else {
                OutputDebug("SetCameraActiveState: Failed to start camera")
                return SetCameraInactiveState()
            }
        }

        ; Show live camera feed
        if (IsObject(webcamControl) && IsObject(capturedImageControl)) {
            ToggleWebcamAndCapturedImage(webcamControl, capturedImageControl, true)
            OutputDebug("SetCameraActiveState: Switched to live camera feed")
        }

        ; Enable photo capture button for active camera
        global ButtonCapturePicture
        if (IsSet(ButtonCapturePicture) && IsObject(ButtonCapturePicture)) {
            ButtonCapturePicture.Enabled := true
            OutputDebug("SetCameraActiveState: Photo capture button enabled")
        }

        ; Update footer with camera status
        UpdateNetworkInfo()

        return true
    } catch as err {
        OutputDebug("SetCameraActiveState: Error activating camera: " err.Message)
        return SetCameraInactiveState()
    }
}

; Function to update the date/time display
UpdateDateTime() {
    global DateText, TimeText
    if IsObject(DateText) && IsObject(TimeText) {
        DateText.Text := FormatTime(, "dd-MMM-yyyy")
        TimeText.Text := FormatTime(, "hh:mm:ss tt")
    }
}

; Function to update network information in the footer
; This is now only called once at startup or when explicitly triggered by user actions
UpdateNetworkInfo() {
    global FooterText, RightThumbprintVerificationEnabled, g_isPostExamMode, g_fingerprintReaderStatus, g_fingerprintDeviceSN, g_cameraStatus

    if (IsSet(FooterText) && IsObject(FooterText)) {
        try {
            ; Get IP and MAC addresses with error handling
            ipAddress := "Unknown"
            macAddress := "Unknown"

            try {
                ipAddress := GetLocalIP()
            } catch Error as e {
                OutputDebug("Error getting IP address in UpdateNetworkInfo: " e.Message)
            }

            try {
                macAddress := GetMACAddress()
            } catch Error as e {
                OutputDebug("Error getting MAC address in UpdateNetworkInfo: " e.Message)
            }

            ; Add thumbprint verification mode to the footer
            thumbprintMode := RightThumbprintVerificationEnabled ? "Dual Thumb" : "Single Thumb"

            ; Add post-exam mode indicator if enabled
            postExamText := g_isPostExamMode ? " | Post-Exam Mode" : ""

            ; Simplify camera status to just "Connected" or "Not Connected"
            cameraStatusText := "Not Connected"
            if (IsSet(g_cameraStatus) && g_cameraStatus != "" && g_cameraStatus != "Not Connected" &&
                g_cameraStatus != "Not Available" && !InStr(g_cameraStatus, "Error")) {
                cameraStatusText := "Connected"
            }

            ; Simplify fingerprint status to "Connected (serial)" or "Not Connected"
            fingerprintStatusText := "Not Connected"

            ; Debug the fingerprint device serial number
            ErrorHandler.LogMessage("DEBUG", "g_fingerprintDeviceSN value: '" g_fingerprintDeviceSN "', type: " Type(g_fingerprintDeviceSN))

            if (IsSet(g_fingerprintReaderStatus) && g_fingerprintReaderStatus != "" && g_fingerprintReaderStatus != "Not Connected" &&
                !InStr(g_fingerprintReaderStatus, "Error") && !InStr(g_fingerprintReaderStatus, "Initializing")) {

                ; Show "Connected" without serial number in footer, but log full details
                fingerprintStatusText := "Connected"

                ; Log full device details for debugging
                if (IsSet(g_fingerprintDeviceSN) && g_fingerprintDeviceSN != "") {
                    ErrorHandler.LogMessage("DEBUG", "Fingerprint device details - Model: " g_fingerprintReaderStatus ", Serial: " g_fingerprintDeviceSN)
                } else {
                    ErrorHandler.LogMessage("DEBUG", "Fingerprint device connected - Model: " g_fingerprintReaderStatus ", No serial number available")
                }
            }

            ; Update the footer text with device status information
            FooterText.Text := " IP: " . ipAddress .
                              " | MAC: " . macAddress .
                              " | Camera: " . cameraStatusText .
                              " | Fingerprint: " . fingerprintStatusText .
                              " | Thumbprint: " . thumbprintMode .
                              postExamText .
                              " | Operator1 (Logged In)"

            ErrorHandler.LogMessage("INFO", "Device status updated in footer: Camera=" g_cameraStatus ", Fingerprint=" fingerprintStatusText)
        } catch Error as e {
            ; If any error occurs, ensure we at least show the device status
            try {
                thumbprintMode := RightThumbprintVerificationEnabled ? "Dual Thumb" : "Single Thumb"
                postExamText := g_isPostExamMode ? " | Post-Exam Mode" : ""

                ; Simplify camera status to just "Connected" or "Not Connected"
                cameraStatusText := "Not Connected"
                if (IsSet(g_cameraStatus) && g_cameraStatus != "" && g_cameraStatus != "Not Connected" &&
                    g_cameraStatus != "Not Available" && !InStr(g_cameraStatus, "Error")) {
                    cameraStatusText := "Connected"
                }

                ; Simplify fingerprint status to "Connected (serial)" or "Not Connected"
                fingerprintStatusText := "Not Connected"

                ; Debug the fingerprint device serial number in error handler
                ErrorHandler.LogMessage("DEBUG", "Error handler - g_fingerprintDeviceSN value: '" g_fingerprintDeviceSN "', type: " Type(g_fingerprintDeviceSN))

                if (IsSet(g_fingerprintReaderStatus) && g_fingerprintReaderStatus != "" && g_fingerprintReaderStatus != "Not Connected" &&
                    !InStr(g_fingerprintReaderStatus, "Error") && !InStr(g_fingerprintReaderStatus, "Initializing")) {

                    ; Show "Connected" without serial number in footer, but log full details
                    fingerprintStatusText := "Connected"

                    ; Log full device details for debugging
                    if (IsSet(g_fingerprintDeviceSN) && g_fingerprintDeviceSN != "") {
                        ErrorHandler.LogMessage("DEBUG", "Error handler - Fingerprint device details - Model: " g_fingerprintReaderStatus ", Serial: " g_fingerprintDeviceSN)
                    } else {
                        ErrorHandler.LogMessage("DEBUG", "Error handler - Fingerprint device connected - Model: " g_fingerprintReaderStatus ", No serial number available")
                    }
                }

                FooterText.Text := " Network: Unavailable" .
                                  " | Camera: " . cameraStatusText .
                                  " | Fingerprint: " . fingerprintStatusText .
                                  " | Thumbprint: " . thumbprintMode .
                                  postExamText .
                                  " | Operator1 (Logged In)"
            } catch {
                ; Last resort if everything fails
                FooterText.Text := " Network information unavailable | Device status unavailable | Operator1 (Logged In)"
            }
            OutputDebug("Error in UpdateNetworkInfo: " e.Message)
        }
    }
}

; Note: We're using the ToggleWebcamAndCapturedImage, ShowControl, and HideControl functions
; from the webcam_utils.ahk library instead of defining our own versions

; Function to automatically dismiss any dialog boxes that appear during webcam operations
DismissDialogsCallback(*) {
    global cameraName

    ; Call the DismissDialogs function from webcam_utils.ahk
    DismissDialogs(cameraName)
}

; Function to check and update fingerprint reader status
; This is now only called once at startup or when explicitly triggered by user actions
CheckFingerprintReaderStatus() {
    global g_fingerprintManager, g_fingerprintReaderStatus, FooterText

    ; Add detailed debugging to log
    ErrorHandler.LogMessage("INFO", "CheckFingerprintReaderStatus called - one-time check")

    ; Check if the variable is set and not empty
    if (!IsSet(g_fingerprintManager) || g_fingerprintManager = "") {
        ErrorHandler.LogMessage("WARNING", "g_fingerprintManager is not set or empty")
        g_fingerprintReaderStatus := "Not Connected"

        ; Update the footer with the new status only if FooterText exists
        if (IsSet(FooterText) && IsObject(FooterText)) {
            UpdateNetworkInfo()
        }
        return
    }

    ; Check if it's actually an object
    if (Type(g_fingerprintManager) != "Object") {
        ErrorHandler.LogMessage("WARNING", "g_fingerprintManager is not an object, type: " Type(g_fingerprintManager))
        g_fingerprintReaderStatus := "Not Connected"

        ; Update the footer with the new status only if FooterText exists
        if (IsSet(FooterText) && IsObject(FooterText)) {
            UpdateNetworkInfo()
        }
        return
    }

    ; Log the current status of the fingerprint manager
    ErrorHandler.LogMessage("DEBUG", "g_fingerprintManager is an object of type: " Type(g_fingerprintManager))
    ErrorHandler.LogMessage("DEBUG", "SecuGenFingerprint class handle: " SecuGenFingerprint.sgfplib)

    ; Check if the fingerprint manager is initialized
    if (!SecuGenFingerprint.sgfplib) {
        ErrorHandler.LogMessage("DEBUG", "Fingerprint manager not initialized, attempting to initialize")
        try {
            g_fingerprintManager.Init()
            ErrorHandler.LogMessage("INFO", "Fingerprint manager initialized successfully")
        } catch as initErr {
            ErrorHandler.LogMessage("WARNING", "Failed to initialize fingerprint manager: " initErr.Message)
            g_fingerprintReaderStatus := "Initialization Error"

            ; Update the footer with the new status only if FooterText exists
            if (IsSet(FooterText) && IsObject(FooterText)) {
                UpdateNetworkInfo()
            }
            return
        }
    }

    try {
        ; Try to get device info to check if reader is connected
        ErrorHandler.LogMessage("DEBUG", "Attempting to get device info")

        ; Check if the GetDeviceInfo method exists
        if (!HasMethod(g_fingerprintManager, "GetDeviceInfo")) {
            ErrorHandler.LogMessage("WARNING", "GetDeviceInfo method not found in g_fingerprintManager")
            g_fingerprintReaderStatus := "Driver Error"

            ; Update the footer with the new status only if FooterText exists
            if (IsSet(FooterText) && IsObject(FooterText)) {
                UpdateNetworkInfo()
            }
            return
        }

        ; Try to get device info
        deviceInfo := g_fingerprintManager.GetDeviceInfo()

        ; If we get here, the device is connected
        g_fingerprintReaderStatus := deviceInfo.DeviceModel
        g_fingerprintDeviceSN := deviceInfo.DeviceSN

        ; Format status for logging
        fingerprintStatusText := "Connected"
        if (g_fingerprintDeviceSN != "N/A") {
            fingerprintStatusText := "Connected (" g_fingerprintDeviceSN ")"
        }

        ErrorHandler.LogMessage("INFO", "Fingerprint reader status updated: " fingerprintStatusText " (internal: " g_fingerprintReaderStatus ")")
    } catch as err {
        ; Check if it's a device not found error (code 2)
        try {
            ErrorHandler.LogMessage("DEBUG", "Error in GetDeviceInfo: " err.Message)

            if (err.HasOwnProp("Extra") && err.Extra = 2) {
                g_fingerprintReaderStatus := "Not Connected"
                ErrorHandler.LogMessage("WARNING", "Fingerprint reader not connected (error code 2)")

                ; Even though the device is not connected, we still have a valid manager object
                ErrorHandler.LogMessage("DEBUG", "Fingerprint manager object is valid but device is not connected")
            } else {
                g_fingerprintReaderStatus := "Error: " SubStr(err.Message, 1, 20) "..."
                ErrorHandler.LogMessage("WARNING", "Error checking fingerprint reader status: " err.Message)
            }

            ; Add more detailed error information
            ErrorHandler.LogMessage("DEBUG", "Full error: " err.Message)
            if (err.HasOwnProp("Extra") && err.Extra != "") {
                ErrorHandler.LogMessage("DEBUG", "Error code: " err.Extra)
            }
        } catch {
            g_fingerprintReaderStatus := "Error: " SubStr(err.Message, 1, 20) "..."
            ErrorHandler.LogMessage("WARNING", "Error checking fingerprint reader status: " err.Message)
        }
    }

    ; Update the footer with the new status only if FooterText exists
    if (IsSet(FooterText) && IsObject(FooterText)) {
        UpdateNetworkInfo()
    }
}

; These functions have been moved to post_exam_utils.ahk

; Function to check all device statuses at once
; This is called once during application startup
CheckDeviceStatus() {
    global g_cameraStatus, capHwnd, isWebcamActive, cameraName, g_fingerprintReaderStatus, g_fingerprintDeviceSN

    ErrorHandler.LogMessage("INFO", "Performing one-time device status check")

    ; Check camera status
    if (capHwnd && isWebcamActive) {
        g_cameraStatus := cameraName ? cameraName : "Connected"
        ErrorHandler.LogMessage("INFO", "Camera status: Connected (internal: " g_cameraStatus ")")
    } else {
        g_cameraStatus := "Not Connected"
        ErrorHandler.LogMessage("INFO", "Camera status: Not Connected")
    }

    ; Check fingerprint reader status - use the status that was already set during initialization
    ; instead of calling CheckFingerprintReaderStatus() which might reset the global variable
    if (!IsSet(g_fingerprintReaderStatus) || g_fingerprintReaderStatus = "") {
        if (SecuGenFingerprint.sgfplib) {
            g_fingerprintReaderStatus := "Auto-detected Device"

            ; Set a default serial number if not already set
            if (!IsSet(g_fingerprintDeviceSN) || g_fingerprintDeviceSN = "") {
                g_fingerprintDeviceSN := "N/A"
            }

            ; Format status for logging
            fingerprintStatusText := "Connected"
            if (g_fingerprintDeviceSN != "N/A") {
                fingerprintStatusText := "Connected (" g_fingerprintDeviceSN ")"
            }

            ErrorHandler.LogMessage("INFO", "Fingerprint reader status: " fingerprintStatusText " (internal: " g_fingerprintReaderStatus ")")
        } else {
            g_fingerprintReaderStatus := "Not Connected"
            g_fingerprintDeviceSN := "N/A"
            ErrorHandler.LogMessage("INFO", "Fingerprint reader not initialized, setting status: Not Connected")
        }
    } else {
        ; Set a default serial number if not already set
        if (!IsSet(g_fingerprintDeviceSN) || g_fingerprintDeviceSN = "") {
            g_fingerprintDeviceSN := "N/A"
        }

        ; Format status for logging
        fingerprintStatusText := "Connected"
        if (g_fingerprintDeviceSN != "N/A") {
            fingerprintStatusText := "Connected (" g_fingerprintDeviceSN ")"
        }

        ErrorHandler.LogMessage("INFO", "Fingerprint reader status: " fingerprintStatusText " (internal: " g_fingerprintReaderStatus ")")
    }

    ; Update network info and footer display
    UpdateNetworkInfo()

    ErrorHandler.LogMessage("INFO", "One-time device status check complete")
}

; Function to get the local IP address
GetLocalIP() {
    ; Try multiple methods to get the IP address, with proper error handling

    ; Method 1: WMI query (most reliable but can have COM issues)
    try {
        ; Create a WMI query to get network adapter configuration
        wmi := ComObject("WbemScripting.SWbemLocator")
        service := wmi.ConnectServer(, "root\CIMV2")
        query := "SELECT IPAddress, Description FROM Win32_NetworkAdapterConfiguration WHERE IPEnabled = True"
        adapters := service.ExecQuery(query)

        ; First try: Look for adapters that are likely to be physical Ethernet or WiFi
        for adapter in adapters {
            try {
                ; Check if IPAddress property exists and is an array
                if (ComObjType(adapter.IPAddress) & 0x2000) { ; VT_ARRAY flag
                    ; Get array length using SafeArray methods instead of .Length
                    ipCount := 0
                    try {
                        ; Try to access the first element to see if array has items
                        firstIP := adapter.IPAddress[0]
                        ipCount := 1

                        ; Try to get more elements if available
                        try {
                            secondIP := adapter.IPAddress[1]
                            ipCount := 2
                        } catch {
                            ; Only one element, that's fine
                        }
                    } catch {
                        ; No elements in array
                        continue
                    }

                    if (ipCount > 0) {
                        desc := adapter.Description
                        ; Skip virtual adapters, VPNs, and loopback
                        if (InStr(desc, "Virtual") || InStr(desc, "VPN") || InStr(desc, "Loopback") || InStr(desc, "Tunnel"))
                            continue

                        ; Iterate through available IPs
                        Loop ipCount {
                            i := A_Index - 1  ; 0-based index
                            try {
                                ip := adapter.IPAddress[i]
                                if (ip && InStr(ip, ":") = 0 && ip != "127.0.0.1") { ; Skip IPv6 addresses and localhost
                                    OutputDebug("Found IP address: " . ip . " from adapter: " . desc)
                                    return ip
                                }
                            } catch {
                                continue
                            }
                        }
                    }
                }
            } catch Error as e {
                OutputDebug("Error accessing adapter IPAddress: " . e.Message)
                continue
            }
        }
    } catch Error as e {
        OutputDebug("Error in WMI method: " . e.Message)
    }

    ; Method 2: Use ipconfig command (more reliable fallback)
    try {
        ; Create a temporary file to store the output
        tempFile := A_Temp "\ip_address.txt"

        ; Run ipconfig and filter for IPv4 addresses
        RunWait(A_ComSpec . " /c ipconfig | findstr IPv4 > `"" . tempFile . "`"", , "Hide")

        if (FileExist(tempFile)) {
            fileContent := FileRead(tempFile)
            FileDelete(tempFile)

            ; Extract IP addresses from the output
            if (RegExMatch(fileContent, "IPv4[^:]*:\s*(\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})", &match)) {
                ip := match[1]
                if (ip != "127.0.0.1") {
                    OutputDebug("Found IP using ipconfig: " . ip)
                    return ip
                }
            }
        }
    } catch Error as e {
        OutputDebug("Error in ipconfig method: " . e.Message)
    }

    ; PowerShell fallback method removed as requested

    return "127.0.0.1" ; Return localhost if no IP found
}

; Function to get the MAC address
GetMACAddress() {
    ; Method 1: WMI query
    try {
        ; Create a WMI query to get network adapter configuration
        wmi := ComObject("WbemScripting.SWbemLocator")
        service := wmi.ConnectServer(, "root\CIMV2")
        query := "SELECT MACAddress, IPEnabled FROM Win32_NetworkAdapterConfiguration WHERE IPEnabled = True"
        adapters := service.ExecQuery(query)

        ; Loop through the results to find a valid MAC address
        for adapter in adapters {
            try {
                macAddress := adapter.MACAddress
                if (macAddress && macAddress != "") {
                    OutputDebug("Found MAC address: " macAddress)
                    return macAddress
                }
            } catch Error as e {
                OutputDebug("Error accessing adapter MACAddress: " e.Message)
                continue
            }
        }
    } catch Error as e {
        OutputDebug("Error in WMI method for MAC address: " e.Message)
    }

    ; Method 2: Use getmac command as fallback
    try {
        tempFile := A_Temp "\mac_address.txt"

        ; Run getmac command to get MAC addresses
        RunWait(A_ComSpec . " /c getmac /v /fo list | findstr Physical > `"" . tempFile . "`"", , "Hide")

        if (FileExist(tempFile)) {
            fileContent := FileRead(tempFile)
            FileDelete(tempFile)

            ; Extract MAC address from the output
            if (RegExMatch(fileContent, "Physical Address:\s*([0-9A-F\-:]+)", &match)) {
                macAddress := match[1]
                OutputDebug("Found MAC using getmac command: " macAddress)
                return macAddress
            }
        }
    } catch Error as e {
        OutputDebug("Error in getmac method: " e.Message)
    }

    ; PowerShell fallback method removed as requested

    return "00:00:00:00:00:00" ; Return default if no MAC found
}

; Function to parse various date formats
DateParse(dateStr) {
    ; Try common date formats
    formats := [
        "yyyy-MM-dd",
        "MM/dd/yyyy",
        "dd/MM/yyyy",
        "yyyy/MM/dd",
        "MMM dd, yyyy",
        "dd MMM yyyy",
        "MMMM dd, yyyy",
        "dd MMMM yyyy"
    ]

    ; Try to parse with each format
    for format in formats {
        try {
            date := ParseDate(dateStr, format)
            if date
                return date
        } catch {
            continue
        }
    }

    return ""
}

; Helper function to parse date with specific format
ParseDate(dateStr, format) {
    ; This is a simplified implementation
    ; In a real implementation, you would use a proper date parsing library

    ; For now, just return the current date as a placeholder
    ; In a real implementation, this would parse the date string according to the format
    return A_Now
}

; ; ShowAboutDialog()
; ; Shows the About dialog with application information
ShowAboutDialog() {
    global myGui, APP_NAME, version

    ; Create the About dialog as a child of the main GUI
    aboutGui := Gui("+AlwaysOnTop +Owner" myGui.Hwnd " -MinimizeBox -MaximizeBox -SysMenu", "About " APP_NAME)
    aboutGui.SetFont("s10", "Segoe UI")

    ; Disable the main GUI while About dialog is open
    myGui.Opt("+Disabled")

    ; Add application logo/icon
    try {
        logoPath := PathManager.GetDatabasePath() "\img\company.jpg"
        if (FileExist(logoPath)) {
            aboutGui.Add("Picture", "x20 y20 w100 h100", logoPath)
        } else {
            ; Fallback to a text label if logo doesn't exist
            aboutGui.Add("Text", "x20 y20 w100 h100 +Border +Center +0x200", "LOGO")
        }
    } catch as err {
        ErrorHandler.LogMessage("WARNING", "Failed to load logo in About dialog: " err.Message)
        aboutGui.Add("Text", "x20 y20 w100 h100 +Border +Center +0x200", "LOGO")
    }

    ; Add application information
    aboutGui.SetFont("s14 bold", "Segoe UI")
    aboutGui.Add("Text", "x140 y20 w300 h30", APP_NAME)

    aboutGui.SetFont("s10 norm", "Segoe UI")
    aboutGui.Add("Text", "x140 y50 w300 h20", "Version: " version)
    aboutGui.Add("Text", "x140 y70 w300 h20", "© 2025 WinCBT")

    ; Add description
    aboutGui.Add("Text", "x20 y130 w420 h80",
        "WinCBT-Biometric is an Exam Verification System designed to manage candidate verification and seat assignment. This system helps exam administrators verify candidate identity through biometric data and assign appropriate seating.")

    ; Add OK button
    OKButton := aboutGui.Add("Button", "x180 y220 w100 h30 +0x1000 Default", "OK")
    OKButton.OnEvent("Click", CloseAboutDialog)

    ; Handle dialog close
    aboutGui.OnEvent("Close", CloseAboutDialog)

    ; Close dialog function
    CloseAboutDialog(*) {
        ; Re-enable the main GUI
        myGui.Opt("-Disabled")

        ; Destroy the dialog
        aboutGui.Destroy()
        ErrorHandler.LogMessage("INFO", "About dialog closed")
    }

    ; Show the dialog
    aboutGui.Show("w460 h270")
}

; ; ShowHelpDialog()
; ; Shows the Help dialog with user manual content
ShowHelpDialog() {
    global myGui, APP_NAME

    ; Create the Help dialog as a child of the main GUI
    helpGui := Gui("+AlwaysOnTop +Owner" myGui.Hwnd " +Resize", "User Manual - " APP_NAME)
    helpGui.SetFont("s10", "Segoe UI")

    ; Disable the main GUI while Help dialog is open
    myGui.Opt("+Disabled")

    ; Load help content from file
    helpContent := ""
    helpFile := A_ScriptDir "\help.txt"

    if (FileExist(helpFile)) {
        try {
            helpContent := FileRead(helpFile)
            ErrorHandler.LogMessage("INFO", "Help content loaded from file")
        } catch as err {
            ErrorHandler.LogMessage("WARNING", "Failed to read help file: " err.Message)
            helpContent := "# User Manual content could not be loaded.`n`nPlease contact your system administrator."
        }
    } else {
        ErrorHandler.LogMessage("WARNING", "Help file not found: " helpFile)
        helpContent := "# User Manual file not found.`n`nPlease contact your system administrator."
    }

    ; Create a tab control for help sections
    Tabs := helpGui.Add("Tab3", "x10 y10 w780 h530", ["Overview", "Candidate Search", "Verification", "Seat Assignment", "Settings", "Troubleshooting"])

    ; Parse the help content to extract sections
    sections := Map()
    currentSection := "Overview"
    sectionContent := ""

    Loop Parse, helpContent, "`n", "`r" {
        if (RegExMatch(A_LoopField, "^## (.+)$", &match)) {
            ; Save previous section content
            if (sectionContent != "") {
                sections[currentSection] := sectionContent
            }

            ; Start new section
            currentSection := match[1]
            sectionContent := ""
        } else {
            ; Add line to current section
            sectionContent .= A_LoopField "`n"
        }
    }

    ; Save the last section
    if (sectionContent != "") {
        sections[currentSection] := sectionContent
    }

    ; Add content to each tab
    Tabs.UseTab(1) ; Overview
    helpGui.Add("Edit", "x20 y40 w760 h490 ReadOnly +Wrap", sections.Has("Introduction") ? sections["Introduction"] : "Overview content not available.")

    Tabs.UseTab(2) ; Candidate Search
    helpGui.Add("Edit", "x20 y40 w760 h490 ReadOnly -Wrap", sections.Has("Candidate Search") ? sections["Candidate Search"] : "Candidate Search content not available.")

    Tabs.UseTab(3) ; Verification
    helpGui.Add("Edit", "x20 y40 w760 h490 ReadOnly -Wrap", sections.Has("Biometric Verification") ? sections["Biometric Verification"] : "Verification content not available.")

    Tabs.UseTab(4) ; Seat Assignment
    helpGui.Add("Edit", "x20 y40 w760 h490 ReadOnly -Wrap", sections.Has("Seat Assignment") ? sections["Seat Assignment"] : "Seat Assignment content not available.")

    Tabs.UseTab(5) ; Settings
    helpGui.Add("Edit", "x20 y40 w760 h490 ReadOnly -Wrap", sections.Has("Settings") ? sections["Settings"] : "Settings content not available.")

    Tabs.UseTab(6) ; Troubleshooting
    helpGui.Add("Edit", "x20 y40 w760 h490 ReadOnly -Wrap", sections.Has("Troubleshooting") ? sections["Troubleshooting"] : "Troubleshooting content not available.")

    ; Return to default tab
    Tabs.UseTab()

    ; Add Close button
    CloseButton := helpGui.Add("Button", "x350 y550 w100 h30 +0x1000 Default", "Close")
    CloseButton.OnEvent("Click", CloseHelpDialog)

    ; Handle dialog close
    helpGui.OnEvent("Close", CloseHelpDialog)

    ; Handle dialog resize
    helpGui.OnEvent("Size", ResizeHelpDialog)

    ; Close dialog function
    CloseHelpDialog(*) {
        ; Re-enable the main GUI
        myGui.Opt("-Disabled")

        ; Destroy the dialog
        helpGui.Destroy()
        ErrorHandler.LogMessage("INFO", "Help dialog closed")
    }

    ; Resize dialog function
    ResizeHelpDialog(thisGui, MinMax, Width, Height) {
        if (MinMax = -1) ; Dialog is minimized
            return

        ; Resize the tab control
        try {
            Tabs.Move(,, Width - 20, Height - 90)

            ; Resize all edit controls inside tabs
            for i, control in thisGui.Controls {
                if (control.Type = "Edit") {
                    control.Move(,, Width - 40, Height - 110)
                }
            }

            ; Move the Close button
            CloseButton.Move((Width / 2) - 50,, 100, 30)
        } catch as err {
            ErrorHandler.LogMessage("WARNING", "Error resizing help dialog: " err.Message)
        }
    }

    ; Show the dialog
    helpGui.Show("w800 h600")
}

; ShowPostExamPasswordDialog()
; Shows a modal password dialog for Post-Exam Mode authentication
; @return: "SUCCESS" if password is correct, "FAILED" if incorrect, "CANCELLED" if user cancelled
ShowPostExamPasswordDialog() {
    global myGui

    ; Disable the main GUI while password dialog is open
    myGui.Opt("+Disabled")

    ; Create the password dialog with professional styling
    passwordGui := Gui("+AlwaysOnTop +Owner" myGui.Hwnd " -MinimizeBox -MaximizeBox -SysMenu", "Post-Exam Mode Authentication")
    passwordGui.SetFont("s10", "Segoe UI")

    ; Set attractive background color (light blue theme)
    passwordGui.BackColor := 0xF0F8FF  ; Alice blue background

    ; Add header with distinctive styling
    headerText := passwordGui.Add("Text", "x10 y10 w380 h35 Center +Border +Background4169E1 cWhite", "🔐 ADMINISTRATOR AUTHENTICATION 🔐")
    headerText.SetFont("s12 Bold")

    ; Add instruction text
    instructionText := passwordGui.Add("Text", "x20 y60 w360 h40 Center", "Enter administrator password to toggle Post-Exam Mode:")
    instructionText.SetFont("s10")

    ; Add password input field
    passwordEdit := passwordGui.Add("Edit", "x50 y110 w300 h25 Password +0x1000")
    passwordEdit.SetFont("s11")

    ; Add error message text (initially hidden)
    errorText := passwordGui.Add("Text", "x20 y145 w360 h25 Center cRed Hidden", "❌ Incorrect password. Please try again.")
    errorText.SetFont("s10 Bold")

    ; Add buttons with professional styling
    okButton := passwordGui.Add("Button", "x80 y180 w100 h35 +0x1000 Default", "✓ OK")
    okButton.SetFont("s10 Bold")
    okButton.Opt("cGreen")

    cancelButton := passwordGui.Add("Button", "x220 y180 w100 h35 +0x1000", "✗ Cancel")
    cancelButton.SetFont("s10 Bold")
    cancelButton.Opt("cRed")

    ; Variables to track dialog result
    dialogResult := "CANCELLED"

    ; Event handlers
    okButton.OnEvent("Click", OkButtonClick)
    cancelButton.OnEvent("Click", CancelButtonClick)
    passwordGui.OnEvent("Close", CancelButtonClick)

    ; Define OK button click handler
    OkButtonClick(*) {
        ; Get the entered password
        enteredPassword := passwordEdit.Text

        ; Verify password
        if (enteredPassword == "admin123") {
            ; Password is correct
            dialogResult := "SUCCESS"
            ClosePasswordDialog()
        } else {
            ; Password is incorrect - show error message
            errorText.Opt("-Hidden")
            passwordEdit.Text := ""  ; Clear the password field
            passwordEdit.Focus()     ; Focus back to password field
            ErrorHandler.LogMessage("WARNING", "Incorrect password entered in Post-Exam Mode dialog")
        }
    }

    ; Define Cancel button click handler
    CancelButtonClick(*) {
        dialogResult := "CANCELLED"
        ClosePasswordDialog()
    }

    ; Define close dialog function
    ClosePasswordDialog() {
        ; Re-enable the main GUI
        myGui.Opt("-Disabled")

        ; Destroy the dialog
        passwordGui.Destroy()
        ErrorHandler.LogMessage("INFO", "Post-Exam Mode password dialog closed with result: " dialogResult)
    }

    ; Show the dialog and focus on password field
    passwordGui.Show("w400 h235")
    passwordEdit.Focus()

    ; Wait for the dialog to close and return the result
    WinWaitClose("ahk_id " passwordGui.Hwnd)
    return dialogResult
}

; ManageSpecialCandidateFingerprintButtons()
; Manages fingerprint capture button states for special candidates based on their accommodation selection
; This ensures that after fingerprint capture, only the appropriate buttons remain enabled
ManageSpecialCandidateFingerprintButtons() {
    try {
        global g_specialAccommodation, ButtonCaptureFingerprint, ButtonCaptureRightFingerprint
        global RightThumbprintVerificationEnabled

        ; Only apply special management if this is a special candidate
        if (!g_specialAccommodation.isSpecialCandidate) {
            OutputDebug("ManageSpecialCandidateFingerprintButtons: Not a special candidate, using standard button management")
            return
        }

        OutputDebug("ManageSpecialCandidateFingerprintButtons: Managing buttons for special candidate")
        OutputDebug("Special accommodation: leftThumb=" g_specialAccommodation.leftThumb ", rightThumb=" g_specialAccommodation.rightThumb)

        ; Apply button states based on special accommodation selection
        if (g_specialAccommodation.leftThumb && g_specialAccommodation.rightThumb) {
            ; Both thumbs selected - enable both buttons if dual thumb mode is enabled
            if (RightThumbprintVerificationEnabled) {
                ButtonCaptureFingerprint.Enabled := true
                ButtonCaptureRightFingerprint.Enabled := true
                OutputDebug("Special candidate: Both thumbs enabled (dual thumb mode)")
            } else {
                ; In single thumb mode, only enable left thumb
                ButtonCaptureFingerprint.Enabled := true
                ButtonCaptureRightFingerprint.Enabled := false
                OutputDebug("Special candidate: Only left thumb enabled (single thumb mode)")
            }
        } else if (g_specialAccommodation.leftThumb) {
            ; Left thumb only - enable left, disable right
            ButtonCaptureFingerprint.Enabled := true
            ButtonCaptureRightFingerprint.Enabled := false
            OutputDebug("Special candidate: Only left thumb enabled")
        } else if (g_specialAccommodation.rightThumb) {
            ; Right thumb only - disable left, enable right
            ButtonCaptureFingerprint.Enabled := false
            ButtonCaptureRightFingerprint.Enabled := true
            OutputDebug("Special candidate: Only right thumb enabled")
        } else {
            ; No thumbs selected (shouldn't happen, but handle gracefully)
            ButtonCaptureFingerprint.Enabled := false
            ButtonCaptureRightFingerprint.Enabled := false
            OutputDebug("Special candidate: No thumbs selected, disabling both buttons")
        }

    } catch as err {
        OutputDebug("ManageSpecialCandidateFingerprintButtons: Error managing buttons: " err.Message)
    }
}

; LogPostExamVerificationCompletion()
; Logs the completion of post-exam verification to application log file
; @param rollNumber: The candidate's roll number
; @param candidateName: The candidate's name
LogPostExamVerificationCompletion(rollNumber, candidateName) {
    try {
        ; Create timestamp
        timestamp := FormatTime(, "yyyy-MM-dd HH:mm:ss")

        ; Get assigned seat information
        assignedSeat := g_dbManager.GetCandidateSeat(rollNumber)

        ; Create log message
        logMessage := timestamp " - POST-EXAM VERIFICATION COMPLETED: "
        logMessage .= "Roll Number: " rollNumber ", "
        logMessage .= "Name: " candidateName ", "
        logMessage .= "Assigned Seat: " assignedSeat ", "
        logMessage .= "All biometric verifications completed successfully"

        ; Ensure logs directory exists
        logsDir := A_ScriptDir "\logs"
        if (!DirExist(logsDir)) {
            DirCreate(logsDir)
        }

        ; Write to application log
        logFile := logsDir "\application.log"
        FileAppend(logMessage "`n", logFile)

        ; Also log to ErrorHandler for consistency
        ErrorHandler.LogMessage("INFO", "Post-exam verification completed for " rollNumber " (" candidateName ")")

        OutputDebug("Post-exam verification completion logged for: " rollNumber)

    } catch as err {
        OutputDebug("Error logging post-exam verification completion: " err.Message)
        ErrorHandler.LogMessage("ERROR", "Failed to log post-exam verification completion: " err.Message)
    }
}

; UpdatePostExamStatusDisplay()
; Updates the post-exam verification status display in the GUI
; @param rollNumber: The candidate's roll number
; @param candidateData: The candidate data object
; @param candidatesPath: Path to the candidates database file
UpdatePostExamStatusDisplay(rollNumber, candidateData, candidatesPath) {
    try {
        ; Check overall verification status
        if (candidateData.PostExamBiometricStatus == "Verified") {
            PostVerificationStatusValue.Text := "Completed"
            PostVerificationStatusValue.Opt("cGreen")
            ErrorHandler.LogMessage("INFO", "Post-exam verification status is Completed for candidate: " rollNumber)
        } else {
            ; Check individual verification statuses to determine if we need to update
            allVerified := true

            ; Photo is always required
            if (candidateData.PostExamPhotoStatus != "Verified") {
                allVerified := false
                ErrorHandler.LogMessage("DEBUG", "Post-exam photo verification incomplete for candidate: " rollNumber)
            }

            ; Check fingerprint verification based on special status and settings
            isSpecialCandidate := false
            try {
                isSpecialCandidate := (candidateData.Special == "1")
            } catch {
                ; If Special field doesn't exist, assume not special
                isSpecialCandidate := false
            }

            if (isSpecialCandidate) {
                ; For special candidates, check if at least one fingerprint is verified
                if (candidateData.PostExamFingerprintStatus != "Verified" && candidateData.PostExamRightFingerprintStatus != "Verified") {
                    allVerified := false
                    ErrorHandler.LogMessage("DEBUG", "Post-exam fingerprint verification incomplete for special candidate: " rollNumber)
                }
            } else {
                ; For regular candidates
                ; Left fingerprint is always required
                if (candidateData.PostExamFingerprintStatus != "Verified") {
                    allVerified := false
                    ErrorHandler.LogMessage("DEBUG", "Post-exam left fingerprint verification incomplete for candidate: " rollNumber)
                }

                ; Right fingerprint is required if enabled
                if (RightThumbprintVerificationEnabled && candidateData.PostExamRightFingerprintStatus != "Verified") {
                    allVerified := false
                    ErrorHandler.LogMessage("DEBUG", "Post-exam right fingerprint verification incomplete for candidate: " rollNumber)
                }
            }

            ; Signature is required if enabled
            if (SignatureVerificationEnabled && candidateData.PostExamSignatureStatus != "Verified") {
                allVerified := false
                ErrorHandler.LogMessage("DEBUG", "Post-exam signature verification incomplete for candidate: " rollNumber)
            }

            ; If all verifications are complete but status is not updated, update it now
            if (allVerified) {
                try {
                    IniWrite("Verified", candidatesPath, rollNumber, "PostExamBiometricStatus")
                    ErrorHandler.LogMessage("INFO", "Updated PostExamBiometricStatus to Verified based on individual statuses")
                    PostVerificationStatusValue.Text := "Completed"
                    PostVerificationStatusValue.Opt("cGreen")

                    ; Log completion of post-exam verification
                    LogPostExamVerificationCompletion(rollNumber, candidateData.Name)
                } catch as err {
                    ErrorHandler.LogMessage("ERROR", "Error updating PostExamBiometricStatus: " err.Message)
                    PostVerificationStatusValue.Text := "Incomplete"
                    PostVerificationStatusValue.Opt("cRed")
                }
            } else {
                PostVerificationStatusValue.Text := "Incomplete"
                PostVerificationStatusValue.Opt("cRed")
                ErrorHandler.LogMessage("INFO", "Post-exam verification status is Incomplete for candidate: " rollNumber)
            }
        }

        ; Update GUI status fields to show current post-exam verification status
        UpdatePostExamGUIStatusFields(candidateData)

    } catch as err {
        ErrorHandler.LogMessage("ERROR", "Error updating post-exam status display: " err.Message)
        PostVerificationStatusValue.Text := "Error"
        PostVerificationStatusValue.Opt("cRed")
    }
}

; UpdatePostExamGUIStatusFields()
; Updates the GUI status fields to reflect post-exam verification status
; @param candidateData: The candidate data object containing post-exam status fields
UpdatePostExamGUIStatusFields(candidateData) {
    try {
        ; Update photo status based on post-exam data
        if (candidateData.PostExamPhotoStatus == "Verified") {
            PhotoStatusValue.Text := "Verified"
            PhotoStatusValue.Opt("cGreen")
        } else if (candidateData.PostExamPhotoStatus != "") {
            PhotoStatusValue.Text := candidateData.PostExamPhotoStatus
            PhotoStatusValue.Opt("cRed")
        }

        ; Update fingerprint status based on post-exam data
        if (candidateData.PostExamFingerprintStatus == "Verified") {
            FingerprintStatusValue.Text := "Verified"
            FingerprintStatusValue.Opt("cGreen")
        } else if (candidateData.PostExamFingerprintStatus != "") {
            FingerprintStatusValue.Text := candidateData.PostExamFingerprintStatus
            FingerprintStatusValue.Opt("cRed")
        }

        ; Update right fingerprint status based on post-exam data
        if (candidateData.PostExamRightFingerprintStatus == "Verified") {
            RightFingerprintStatusValue.Text := "Verified"
            RightFingerprintStatusValue.Opt("cGreen")
        } else if (candidateData.PostExamRightFingerprintStatus != "") {
            RightFingerprintStatusValue.Text := candidateData.PostExamRightFingerprintStatus
            RightFingerprintStatusValue.Opt("cRed")
        }

        ; Update signature status based on post-exam data
        if (SignatureVerificationEnabled) {
            if (candidateData.PostExamSignatureStatus == "Verified") {
                SignatureStatusValue.Text := "Verified"
                SignatureStatusValue.Opt("cGreen")
            } else if (candidateData.PostExamSignatureStatus != "") {
                SignatureStatusValue.Text := candidateData.PostExamSignatureStatus
                SignatureStatusValue.Opt("cRed")
            }
        }

        ErrorHandler.LogMessage("DEBUG", "Updated GUI status fields for post-exam mode")
    } catch as err {
        ErrorHandler.LogMessage("ERROR", "Error updating post-exam GUI status fields: " err.Message)
    }
}

; Show the GUI
myGui.Show("w1280 h700")

; Perform post-GUI device initialization
PerformPostGUIInitialization()

; ; PerformPostGUIInitialization()
; ; Performs device initialization after GUI is displayed and visible to the user
; ; Combines camera verification and fingerprint LED blink test in a unified sequence
PerformPostGUIInitialization() {
    OutputDebug("PerformPostGUIInitialization: Starting post-GUI device initialization")

    try {
        global sbMain, g_fingerprintManager, g_cameraStatus, g_fingerprintReaderStatus

        ; Update status to show initialization is starting
        if (IsSet(sbMain) && IsObject(sbMain)) {
            try {
                sbMain.Text := "Initializing devices..."
                OutputDebug("PerformPostGUIInitialization: Status bar updated - Initializing devices")
            } catch as err {
                OutputDebug("PerformPostGUIInitialization: Error updating status bar: " err.Message)
            }
        }

        ; Small delay to ensure GUI is fully rendered
        Sleep(500)

        ; === CAMERA INITIALIZATION AND VERIFICATION ===
        OutputDebug("PerformPostGUIInitialization: Starting camera initialization")

        try {
            ; Perform camera initialization with brief verification display
            if (InitializeCameraOnStartup()) {
                OutputDebug("PerformPostGUIInitialization: Camera initialization successful")
                if (IsSet(sbMain) && IsObject(sbMain)) {
                    try {
                        sbMain.Text := "Camera verified, testing fingerprint reader..."
                        OutputDebug("PerformPostGUIInitialization: Status bar updated - Camera verified")
                    } catch as err {
                        OutputDebug("PerformPostGUIInitialization: Error updating status bar: " err.Message)
                    }
                }
            } else {
                OutputDebug("PerformPostGUIInitialization: Camera initialization failed")
                if (IsSet(sbMain) && IsObject(sbMain)) {
                    try {
                        sbMain.Text := "Camera failed, testing fingerprint reader..."
                        OutputDebug("PerformPostGUIInitialization: Status bar updated - Camera failed")
                    } catch as err {
                        OutputDebug("PerformPostGUIInitialization: Error updating status bar: " err.Message)
                    }
                }
            }
        } catch as err {
            OutputDebug("PerformPostGUIInitialization: Camera initialization error: " err.Message)
            g_cameraStatus := "Error: " SubStr(err.Message, 1, 20) "..."
            if (IsSet(sbMain) && IsObject(sbMain)) {
                try {
                    sbMain.Text := "Camera error, testing fingerprint reader..."
                    OutputDebug("PerformPostGUIInitialization: Status bar updated - Camera error")
                } catch as err2 {
                    OutputDebug("PerformPostGUIInitialization: Error updating status bar: " err2.Message)
                }
            }
        }

        ; Small delay between camera and fingerprint tests
        Sleep(100)

        ; === FINGERPRINT READER LED BLINK TEST ===
        OutputDebug("PerformPostGUIInitialization: Starting fingerprint LED blink test")

        ; Debug the fingerprint manager state
        OutputDebug("PerformPostGUIInitialization: g_fingerprintManager IsSet: " (IsSet(g_fingerprintManager) ? "True" : "False"))
        if (IsSet(g_fingerprintManager)) {
            OutputDebug("PerformPostGUIInitialization: g_fingerprintManager IsObject: " (IsObject(g_fingerprintManager) ? "True" : "False"))
            OutputDebug("PerformPostGUIInitialization: g_fingerprintManager Type: " Type(g_fingerprintManager))
            OutputDebug("PerformPostGUIInitialization: g_fingerprintManager Value: " String(g_fingerprintManager))
        }

        ; Ensure fingerprint manager is properly initialized before LED test
        if (IsSet(g_fingerprintManager) && IsObject(g_fingerprintManager)) {
            ; Additional initialization check and small delay for device readiness
            try {
                ; Verify the device is properly initialized
                if (!SecuGenFingerprint.sgfplib) {
                    OutputDebug("PerformPostGUIInitialization: Fingerprint device not initialized, attempting initialization")
                    g_fingerprintManager.Init()
                    Sleep(500)  ; Give device time to initialize
                }
            } catch as initErr {
                OutputDebug("PerformPostGUIInitialization: Error during fingerprint device initialization: " initErr.Message)
            }
            try {
                ; Blink LED twice to indicate successful initialization
                result := g_fingerprintManager.SetLedOn(true)  ; Turn LED on
                Sleep(300)                        ; Wait 300ms

                result := g_fingerprintManager.SetLedOn(false)  ; Turn LED off
                Sleep(200)                        ; Wait 200ms

                result := g_fingerprintManager.SetLedOn(true)  ; Turn LED on
                Sleep(300)                        ; Wait 300ms

                result := g_fingerprintManager.SetLedOn(false)  ; Turn LED off

                ErrorHandler.LogMessage("INFO", "Fingerprint reader LED blinked twice during post-GUI initialization")
                OutputDebug("PerformPostGUIInitialization: Fingerprint LED blink test successful")

                if (IsSet(sbMain) && IsObject(sbMain)) {
                    try {
                        sbMain.Text := "Fingerprint reader tested successfully..."
                        OutputDebug("PerformPostGUIInitialization: Status bar updated - Fingerprint tested")
                    } catch as err {
                        OutputDebug("PerformPostGUIInitialization: Error updating status bar: " err.Message)
                    }
                }
            } catch as ledErr {
                ErrorHandler.LogMessage("WARNING", "Failed to blink fingerprint reader LED during post-GUI init: " ledErr.Message)
                OutputDebug("PerformPostGUIInitialization: Fingerprint LED blink test failed: " ledErr.Message)

                try {
                    if (ledErr.HasOwnProp("Extra") && ledErr.Extra != "") {
                        ErrorHandler.LogMessage("DEBUG", "LED error code: " ledErr.Extra)

                        ; If error code is 2 (device not found), update status but keep the manager object
                        if (ledErr.Extra = 2) {
                            g_fingerprintReaderStatus := "Not Connected"
                            ErrorHandler.LogMessage("WARNING", "Fingerprint reader not physically connected (error code 2)")
                        }
                    }
                } catch {
                    ; Ignore errors in error handling
                }

                if (IsSet(sbMain) && IsObject(sbMain)) {
                    try {
                        sbMain.Text := "Fingerprint reader test failed..."
                        OutputDebug("PerformPostGUIInitialization: Status bar updated - Fingerprint test failed")
                    } catch as err {
                        OutputDebug("PerformPostGUIInitialization: Error updating status bar: " err.Message)
                    }
                }
            }
        } else {
            OutputDebug("PerformPostGUIInitialization: Fingerprint manager not available for LED test")

            ; Try to reinitialize the fingerprint manager if it's not available
            try {
                OutputDebug("PerformPostGUIInitialization: Attempting to reinitialize fingerprint manager")
                g_fingerprintManager := SecuGenFingerprint()
                g_fingerprintManager.Init()

                OutputDebug("PerformPostGUIInitialization: Fingerprint manager reinitialized successfully")

                ; Try the LED blink test again
                try {
                    result := g_fingerprintManager.SetLedOn(true)
                    Sleep(300)
                    result := g_fingerprintManager.SetLedOn(false)
                    Sleep(200)
                    result := g_fingerprintManager.SetLedOn(true)
                    Sleep(300)
                    result := g_fingerprintManager.SetLedOn(false)

                    ErrorHandler.LogMessage("INFO", "Fingerprint reader LED blinked twice after reinitialization")
                    OutputDebug("PerformPostGUIInitialization: LED blink successful after reinitialization")

                    if (IsSet(sbMain) && IsObject(sbMain)) {
                        try {
                            sbMain.Text := "Fingerprint reader reinitialized and tested..."
                            OutputDebug("PerformPostGUIInitialization: Status bar updated - Fingerprint reinitialized")
                        } catch as err {
                            OutputDebug("PerformPostGUIInitialization: Error updating status bar: " err.Message)
                        }
                    }
                } catch as ledErr {
                    OutputDebug("PerformPostGUIInitialization: LED blink failed after reinitialization: " ledErr.Message)
                    if (IsSet(sbMain) && IsObject(sbMain)) {
                        try {
                            sbMain.Text := "Fingerprint reader reinitialized but LED test failed..."
                            OutputDebug("PerformPostGUIInitialization: Status bar updated - LED test failed after reinit")
                        } catch as err {
                            OutputDebug("PerformPostGUIInitialization: Error updating status bar: " err.Message)
                        }
                    }
                }
            } catch as reinitErr {
                OutputDebug("PerformPostGUIInitialization: Failed to reinitialize fingerprint manager: " reinitErr.Message)
                if (IsSet(sbMain) && IsObject(sbMain)) {
                    try {
                        sbMain.Text := "Fingerprint reader not available..."
                        OutputDebug("PerformPostGUIInitialization: Status bar updated - Fingerprint not available")
                    } catch as err {
                        OutputDebug("PerformPostGUIInitialization: Error updating status bar: " err.Message)
                    }
                }
            }
        }

        ; Small delay before showing completion
        Sleep(1000)

        ; === FINALIZE INITIALIZATION ===
        OutputDebug("PerformPostGUIInitialization: Finalizing device initialization")

        ; Update footer with final device status
        try {
            UpdateNetworkInfo()
        } catch as err {
            OutputDebug("PerformPostGUIInitialization: Error updating network info: " err.Message)
        }

        ; Show completion status
        if (IsSet(sbMain) && IsObject(sbMain)) {
            try {
                sbMain.Text := "Device initialization complete | Operator: Admin"
                OutputDebug("PerformPostGUIInitialization: Status bar updated - Initialization complete")
            } catch as err {
                OutputDebug("PerformPostGUIInitialization: Error updating status bar: " err.Message)
            }
        }

        OutputDebug("PerformPostGUIInitialization: Post-GUI device initialization completed")

    } catch as err {
        ErrorHandler.LogMessage("ERROR", "Error during post-GUI initialization: " err.Message)
        OutputDebug("PerformPostGUIInitialization: Error during initialization: " err.Message)

        if (IsSet(sbMain) && IsObject(sbMain)) {
            try {
                sbMain.Text := "Device initialization error | Operator: Admin"
                OutputDebug("PerformPostGUIInitialization: Status bar updated - Initialization error")
            } catch as err2 {
                OutputDebug("PerformPostGUIInitialization: Error updating status bar: " err2.Message)
            }
        }
    }
}

; ; Constructor()
; ; Builds and initializes the main GUI for the application.
; ; Sets up global variables, initializes managers (DB, Webcam), reads configuration,
; ; creates GUI controls, defines event handlers, and sets initial states.
; ; @return: The main Gui object.
Constructor()
{
    ; Initialize path variables for captured images
    global capturedPhotoPath := ""
    global tempCapturedPhotoPath := ""  ; Add new global for temporary storage
    global capturedFingerprintPath := ""
    global capturedSignaturePath := ""
    global candidateData := {}  ; Initialize global candidateData object

    ; Log constructor start
    ErrorHandler.LogMessage("INFO", "Initializing application components")

    ; Initialize database manager with error handling
    try {
        g_dbManager := DBManager()
        ErrorHandler.LogMessage("INFO", "Database manager initialized successfully")
    } catch as err {
        ErrorHandler.LogMessage("CRITICAL", "Failed to initialize database manager: " err.Message)
        ErrorHandler.ShowError("Failed to initialize database manager: " err.Message,
                              APP_NAME " - Critical Error", "Icon! 262144")
    }

    ; Initialize fingerprint manager with error handling
    try {
        ErrorHandler.LogMessage("DEBUG", "Attempting to initialize SecuGenFingerprint")
        g_fingerprintManager := SecuGenFingerprint()
        g_fingerprintManager.Init()

        ; Verify the object was created properly
        ErrorHandler.LogMessage("DEBUG", "g_fingerprintManager type after creation: " Type(g_fingerprintManager))
        ErrorHandler.LogMessage("DEBUG", "IsObject check after creation: " (IsObject(g_fingerprintManager) ? "True" : "False"))

        ; Set the fingerprint reader status to the device model or a default value
        g_fingerprintReaderStatus := "Initializing..."

        ErrorHandler.LogMessage("INFO", "SecuGen fingerprint manager initialized successfully")

        ; LED blink test will be performed after GUI is shown
        ErrorHandler.LogMessage("INFO", "Fingerprint manager initialized - LED test will be performed after GUI display")

        ; Get device info directly to update status
        try {
            deviceInfo := g_fingerprintManager.GetDeviceInfo()
            g_fingerprintReaderStatus := deviceInfo.DeviceModel
            g_fingerprintDeviceSN := deviceInfo.DeviceSN

            ; Format status for logging
            fingerprintStatusText := "Connected"
            if (g_fingerprintDeviceSN != "N/A") {
                fingerprintStatusText := "Connected (" g_fingerprintDeviceSN ")"
            }

            ErrorHandler.LogMessage("INFO", "Fingerprint reader status set to: " fingerprintStatusText " (internal: " g_fingerprintReaderStatus ")")
        } catch as deviceErr {
            ErrorHandler.LogMessage("WARNING", "Error getting device info: " deviceErr.Message)

            ; Check if it's a device not found error (code 2)
            if (deviceErr.HasOwnProp("Extra") && deviceErr.Extra = 2) {
                g_fingerprintReaderStatus := "Not Connected"
                g_fingerprintDeviceSN := "N/A"
                ErrorHandler.LogMessage("WARNING", "Fingerprint reader not connected (error code 2)")
            } else {
                g_fingerprintReaderStatus := "Error: " SubStr(deviceErr.Message, 1, 20) "..."
                g_fingerprintDeviceSN := "N/A"
            }
        }

        ; Verify g_fingerprintManager is still valid after all operations
        ErrorHandler.LogMessage("DEBUG", "Final check - g_fingerprintManager is " (IsSet(g_fingerprintManager) && IsObject(g_fingerprintManager) ? "valid" : "invalid"))
        if (IsSet(g_fingerprintManager) && IsObject(g_fingerprintManager)) {
            ErrorHandler.LogMessage("DEBUG", "g_fingerprintManager class handle: " SecuGenFingerprint.sgfplib)
        }
    } catch as err {
        ErrorHandler.LogMessage("WARNING", "Error initializing fingerprint manager: " err.Message)
        try {
            if (err.HasOwnProp("Extra") && err.Extra != "") {
                ErrorHandler.LogMessage("DEBUG", "Initialization error code: " err.Extra)
            }
        }
        g_fingerprintManager := ""
        g_fingerprintReaderStatus := "Not Connected"

        ; Show warning but don't prevent application from starting
        MsgBox("Fingerprint scanner not detected or driver not installed.`n`n"
             . "The application will run in limited mode without fingerprint functionality.",
             APP_NAME " - Hardware Warning", "Icon!")
    }

    ; Create Main GUI first before adding controls
    myGui := Gui()
    myGui.Title := APP_NAME . " " . version
    myGui.SetFont("s10", "Segoe UI")

    ; Create status bar early to avoid reference errors in callbacks
    sbMain := myGui.Add("StatusBar", "", "Ready | Camera: Initializing... | Operator: Admin")

    ; Validate default photo image exists
    if (!FileExist("img\default_photo.png")) {
        ErrorHandler.LogMessage("WARNING", "Default photo image not found, using placeholder")
        defaultPhotoPath := "img\gray.png"
    } else {
        defaultPhotoPath := "img\default_photo.png"
    }

    ; Create two overlapping controls for webcam feed and captured image
    ; First, create the webcam feed control (Text control for live feed)
    global webcamControl := myGui.Add("Text", "x460 y120 w375 h330 +Border +0x200 BackgroundSilver +Center", "No Live Camera Feed")

    ; Then create the captured image control (Picture control for displaying captured images)
    global capturedImageControl := myGui.Add("Picture", "x460 y120 w375 h330 +Border", defaultPhotoPath)

    ; Set initial camera state to uniform inactive state
    SetCameraInactiveState()

    ; Read camera settings from config file
    try {
        configFile := A_ScriptDir "\WinCBT-Biometric.ini"
        if (FileExist(configFile)) {
            global cameraName := IniRead(configFile, "Camera", "CameraName", "")
            ErrorHandler.LogMessage("INFO", "Read camera name from config: " cameraName)
        } else {
            global cameraName := ""
            ErrorHandler.LogMessage("WARNING", "No camera configuration found")
        }
    } catch as err {
        ErrorHandler.LogMessage("WARNING", "Error reading camera settings: " err.Message)
    }

    ; Set up auto-dismissal of dialog boxes
    global dismissDialogsTimer := SetTimer(DismissDialogsCallback, 100)

    ; Camera and device initialization will be performed after GUI is shown
    StatusCallback("Preparing device initialization... | Operator: Admin")

    ; We'll update the footer later after FooterText is created

    ; Read configuration settings with error handling
    configFile := PathManager.ConfigFile

    ; Validate config file exists
    if (!FileExist(configFile)) {
        ErrorHandler.LogMessage("CRITICAL", "Configuration file not found: " configFile)
        ErrorHandler.ShowError("Configuration file not found. Creating default configuration.",
                              APP_NAME " - Configuration Error", "Icon!")

        ; Create default configuration
        defaultConfig := "[Settings]`n"
                      . "LogLevel=Info`n"
                      . "AutoApprove=0`n"
                      . "RandomSeatAssignment=1`n`n"
                      . "[BiometricDevices]`n"
                      . "FingerprintScanner=Simulated`n"
                      . "SignaturePad=Simulated`n`n"
                      . "[Camera]`n"
                      . "CameraIndex=0`n"
                      . "CameraName=HD Pro Webcam C920`n`n"
                      . "[Verification]`n"
                      . "SignatureVerification=0`n"
                      . "PhotoVerificationMode=Both`n"
                      . "SignatureVerificationMode=Both`n"
                      . "FingerprintVerificationMode=Both`n"
                      . "EnablePostExamVerification=0`n"
                      . "RightThumbprintVerification=1`n"
                      . "PhotoConfidenceThreshold=85`n"
                      . "SignatureConfidenceThreshold=85`n"
                      . "FingerprintConfidenceThreshold=85`n"
                      . "FingerprintCaptureThreshold=70`n"
                      . "FingerprintAutoSaveThreshold=80`n"
                      . "FingerprintMode=save`n`n"
                      . "[Paths]`n"
                      . "dbPath=db`n"

        try {
            FileAppend(defaultConfig, configFile)
            ErrorHandler.LogMessage("INFO", "Created default configuration file")
        } catch as err {
            ErrorHandler.LogMessage("CRITICAL", "Failed to create default configuration: " err.Message)
        }
    }

    ; Helper function to safely read integers from ini files
    ; ; SafeReadInteger(...)
    ; ; Reads an integer value from an INI file, handling potential comments and ensuring type safety.
    ; ; @param filename: The path to the INI file.
    ; ; @param section: The section within the INI file.
    ; ; @param key: The key within the section.
    ; ; @param defaultValue: The value to return if the key is not found or invalid.
    ; ; @return: The integer value read from the INI file or the default value.
    SafeReadInteger(filename, section, key, defaultValue) {
        try {
            value := IniRead(filename, section, key, defaultValue)
            ; Remove any trailing comments or whitespace
            if (InStr(value, ";")) {
                value := Trim(SubStr(value, 1, InStr(value, ";") - 1))
            }

            ; Convert to integer safely
            intValue := Integer(value)
            ErrorHandler.LogMessage("INFO", "Config: " section "." key " = " intValue)
            return intValue
        } catch as err {
            ErrorHandler.LogMessage("WARNING", "Error reading config value " section "." key ": " err.Message)
            ErrorHandler.LogMessage("INFO", "Using default value for " section "." key ": " defaultValue)
            return Integer(defaultValue)
        }
    }

    ; Helper function to safely read string values from ini files
    ; ; SafeReadString(...)
    ; ; Reads a string value from an INI file, handling potential comments and ensuring type safety.
    ; ; @param filename: The path to the INI file.
    ; ; @param section: The section within the INI file.
    ; ; @param key: The key within the section.
    ; ; @param defaultValue: The value to return if the key is not found.
    ; ; @return: The string value read from the INI file or the default value.
    SafeReadString(filename, section, key, defaultValue) {
        try {
            value := IniRead(filename, section, key, defaultValue)
            ; Remove any trailing comments or whitespace
            if (InStr(value, ";")) {
                value := Trim(SubStr(value, 1, InStr(value, ";") - 1))
            }

            ErrorHandler.LogMessage("INFO", "Config: " section "." key " = " value)
            return value
        } catch as err {
            ErrorHandler.LogMessage("WARNING", "Error reading config value " section "." key ": " err.Message)
            ErrorHandler.LogMessage("INFO", "Using default value for " section "." key ": " defaultValue)
            return defaultValue
        }
    }

    ; Read verification settings
    global SignatureVerificationEnabled := SafeReadInteger(configFile, "Verification", "SignatureVerification", "1")
    global RightThumbprintVerificationEnabled := SafeReadInteger(configFile, "Verification", "RightThumbprintVerification", "1")
    global PostExamModeEnabled := SafeReadInteger(configFile, "Verification", "EnablePostExamVerification", "0")

    ; Update window title to show post-exam mode if enabled
    myGui.Title := APP_NAME . (PostExamModeEnabled ? " [PostExam] " : " ") . version
    if (PostExamModeEnabled) {
        ErrorHandler.LogMessage("INFO", "Post-exam mode is enabled")
    }

    ; Read verification modes
    global PhotoVerificationMode := SafeReadString(configFile, "Verification", "PhotoVerificationMode", "Auto")
    global SignatureVerificationMode := SafeReadString(configFile, "Verification", "SignatureVerificationMode", "Auto")
    global FingerprintVerificationMode := SafeReadString(configFile, "Verification", "FingerprintVerificationMode", "Auto")
    global FingerprintMode := SafeReadString(configFile, "Verification", "FingerprintMode", "compare")

    ; Read confidence thresholds
    global PhotoConfidenceThreshold := SafeReadInteger(configFile, "Verification", "PhotoConfidenceThreshold", "85")
    global SignatureConfidenceThreshold := SafeReadInteger(configFile, "Verification", "SignatureConfidenceThreshold", "80")
    global FingerprintConfidenceThreshold := SafeReadInteger(configFile, "Verification", "FingerprintConfidenceThreshold", "90")
    global FingerprintCaptureThreshold := SafeReadInteger(configFile, "Verification", "FingerprintCaptureThreshold", "75")
    global FingerprintAutoSaveThreshold := SafeReadInteger(configFile, "Verification", "FingerprintAutoSaveThreshold", "90")
    global AllowMissingFingerprint := SafeReadInteger(configFile, "Verification", "AllowMissingFingerprint", "1")

    ; Get database path from PathManager
    global dbPath := PathManager.GetDatabasePath(true) ; true to validate and create if missing

    ; Define MenuHandler function before using it
    ; ; MenuHandler(...)
    ; ; Handles menu item selections by routing to appropriate functions.
    ; ; @param ItemName: The name of the selected menu item.
    ; ; @param ItemPos: The position index of the selected menu item.
    ; ; @param MenuName: The name of the menu the item belongs to.
    MenuHandler(ItemName, ItemPos, MenuName) {
        global sbMain, myGui  ; Add myGui to global variables

        ; Handle specific menu items
        if (ItemName == "Settings...") {
            StatusCallback("Opening Settings dialog...")

            ; Call the settings dialog
            try {
                if (ShowSettingsDialog()) {
                    StatusCallback("Settings saved successfully")

                    ; Reload configuration settings
                    configFile := PathManager.ConfigFile

                    ; Read verification settings
                    global SignatureVerificationEnabled := SafeReadInteger(configFile, "Verification", "SignatureVerification", "1")
                    global RightThumbprintVerificationEnabled := SafeReadInteger(configFile, "Verification", "RightThumbprintVerification", "1")
                    global PostExamModeEnabled := SafeReadInteger(configFile, "Verification", "EnablePostExamVerification", "0")

                    ; Update window title to show post-exam mode if enabled
                    titleText := PostExamModeEnabled ? "Exam Verification System [PostExam]" : "Exam Verification System"

                    ; Update the GUI title - ensure consistent format
                    myGui.Title := APP_NAME . (PostExamModeEnabled ? " [PostExam] " : " ") . version

                    ; Update the title text control if it exists with appropriate color
                    if (IsObject(TitleText)) {
                        TitleText.Text := titleText
                        ; Change text color to red when enabled, white when disabled
                        if (PostExamModeEnabled)
                            TitleText.Opt("cRed")
                        else
                            TitleText.Opt("cWhite")
                    }

                    ; Read verification modes
                    global PhotoVerificationMode := IniRead(configFile, "Verification", "PhotoVerificationMode", "Auto")
                    global SignatureVerificationMode := IniRead(configFile, "Verification", "SignatureVerificationMode", "Auto")
                    global FingerprintVerificationMode := IniRead(configFile, "Verification", "FingerprintVerificationMode", "Auto")

                    ; Read confidence thresholds
                    global PhotoConfidenceThreshold := SafeReadInteger(configFile, "Verification", "PhotoConfidenceThreshold", "85")
                    global SignatureConfidenceThreshold := SafeReadInteger(configFile, "Verification", "SignatureConfidenceThreshold", "80")
                    global FingerprintConfidenceThreshold := SafeReadInteger(configFile, "Verification", "FingerprintConfidenceThreshold", "90")

                    ; Read database path from config
                    global dbPath := IniRead(configFile, "Paths", "dbPath", "db")

                    ; Update signature verification UI elements based on new settings
                    if (!SignatureVerificationEnabled) {
                        SignatureCaptureImage.Value := "img\gray.png"
                        SignatureCaptureImage.Opt("Disabled")
                        ButtonCaptureSignature.Enabled := false

                        SignatureVerifyImage.Value := "img\gray.png"
                        SignatureVerifyImage.Opt("Disabled")
                        SignatureStatusValue.Text := "Disabled"
                        SignatureStatusValue.Opt("cGray")
                        ButtonVerifySignature.Enabled := false
                        ButtonVerifySignature.Text := "Disabled"
                    }

                    ; Update camera settings but don't automatically restart camera
                    try {
                        ; Read the updated camera name from config
                        global cameraName := IniRead(configFile, "Camera", "CameraName", "")
                        OutputDebug("Read updated camera name from config: " cameraName)

                        ; Set camera to inactive state after settings change
                        SetCameraInactiveState()

                        ; Update camera status
                        g_cameraStatus := "Settings Updated"
                        ErrorHandler.LogMessage("INFO", "Camera settings updated, camera set to inactive state")

                        SafeUpdateStatusBar("Settings saved - Camera settings updated")
                    } catch as err {
                        OutputDebug("Error updating camera settings: " err.Message)
                        g_cameraStatus := "Error: " SubStr(err.Message, 1, 20) "..."
                        ErrorHandler.LogMessage("WARNING", "Camera status updated: " g_cameraStatus)
                        SafeUpdateStatusBar("Settings saved but camera update failed: " err.Message)
                    }

                    ; Update the footer to reflect the new status
                    UpdateNetworkInfo()
                } else {
                    SafeUpdateStatusBar("Settings dialog closed without saving")
                }
            } catch as err {
                errorMsg := "Error in settings dialog: " . err.Message
                SafeUpdateStatusBar(errorMsg)
                OutputDebug(errorMsg)
                MsgBox(errorMsg, "Settings Error", "Icon! 262208")
            }

            return
        } else if (ItemName == "Logout") {
            SafeUpdateStatusBar("Logout selected - This feature is not yet implemented")
            return
        } else if (ItemName == "Post-Exam Mode") {
            SafeUpdateStatusBar("Post-Exam Mode selected - Checking master control...")
            try {
                ; First check master control in db\config.ini
                dbConfigFile := PathManager.GetDatabasePath() . "\config.ini"
                masterControlEnabled := 0

                try {
                    masterControlEnabled := IniRead(dbConfigFile, "Verification", "MasterPostExamControl", "0")
                } catch as err {
                    ErrorHandler.LogMessage("WARNING", "Error reading MasterPostExamControl setting: " err.Message)
                    masterControlEnabled := 0
                }

                ; Check if master control is enabled
                if (masterControlEnabled != "1") {
                    MsgBox("Post-exam mode is disabled by administrator in the master configuration.",
                           "Post-Exam Mode Disabled", "Icon!")
                    SafeUpdateStatusBar("Post-Exam Mode disabled by master control")
                    ErrorHandler.LogMessage("INFO", "Post-Exam Mode toggle attempted but master control is disabled")
                    return
                }

                ; Master control is enabled, now prompt for password
                SafeUpdateStatusBar("Requesting password for Post-Exam Mode toggle...")

                ; Show password dialog and get result
                passwordResult := ShowPostExamPasswordDialog()

                ; Check if user cancelled or authentication failed
                if (passwordResult == "CANCELLED") {
                    SafeUpdateStatusBar("Post-Exam Mode toggle cancelled")
                    ErrorHandler.LogMessage("INFO", "Post-Exam Mode toggle cancelled by user")
                    return
                } else if (passwordResult == "FAILED") {
                    SafeUpdateStatusBar("Post-Exam Mode authentication failed - incorrect password")
                    ErrorHandler.LogMessage("WARNING", "Post-Exam Mode authentication failed - incorrect password")
                    return
                } else if (passwordResult != "SUCCESS") {
                    SafeUpdateStatusBar("Post-Exam Mode authentication error")
                    ErrorHandler.LogMessage("ERROR", "Unexpected result from password dialog: " passwordResult)
                    return
                }

                ; Password correct, toggle the local setting
                configFile := PathManager.ConfigFile
                global PostExamModeEnabled

                ; Toggle the post-exam mode
                newValue := PostExamModeEnabled ? "0" : "1"
                IniWrite(newValue, configFile, "Verification", "EnablePostExamVerification")

                ; Update the global variable
                PostExamModeEnabled := (newValue == "1")

                ; Update the menu check state
                if (PostExamModeEnabled) {
                    ManageMenu.Check("Post-Exam Mode")
                } else {
                    ManageMenu.Uncheck("Post-Exam Mode")
                }

                ; Update the title text control using the helper function
                UpdateTitleBar()

                ; Update the GUI title
                myGui.Title := APP_NAME . (PostExamModeEnabled ? " [PostExam] " : " ") . version

                ; Update the status bar
                SafeUpdateStatusBar("Post-Exam Mode: " . (PostExamModeEnabled ? "Enabled" : "Disabled"))

                ; Log the change
                ErrorHandler.LogMessage("INFO", "Post-Exam Mode " . (PostExamModeEnabled ? "enabled" : "disabled") . " by user")

                ; Show confirmation message
                ;MsgBox("Post-Exam Mode has been " . (PostExamModeEnabled ? "enabled" : "disabled") . ".",
                ;       "Post-Exam Mode", "Icon! 262208")
            } catch as err {
                ErrorHandler.LogMessage("ERROR", "Error toggling Post-Exam Mode: " err.Message)
                SafeUpdateStatusBar("Error toggling Post-Exam Mode")
                MsgBox("An error occurred while toggling Post-Exam Mode: " . err.Message,
                       "Error", "Icon! 262208")
            }
            return
        } else if (ItemName == "User Manual") {
            SafeUpdateStatusBar("Opening User Manual...")
            try {
                ShowHelpDialog()
                SafeUpdateStatusBar("User Manual opened")
            } catch as err {
                ErrorHandler.LogMessage("ERROR", "Error showing help dialog: " err.Message)
                SafeUpdateStatusBar("Error showing help dialog")
            }
            return
        } else if (ItemName == "About WinCBT-Biometric") {
            SafeUpdateStatusBar("Opening About dialog...")
            try {
                ShowAboutDialog()
                SafeUpdateStatusBar("About dialog opened")
            } catch as err {
                ErrorHandler.LogMessage("ERROR", "Error showing about dialog: " err.Message)
                SafeUpdateStatusBar("Error showing about dialog")
            }
            return
        }

        ; Default handler for other menu items
        SafeUpdateStatusBar("Menu action selected - This feature is not yet implemented")
    }

    ; Create Menu Structure
    FileMenu := Menu()
    FileMenu.Add("Settings...", MenuHandler)
    FileMenu.Add("Logout", MenuHandler)
    FileMenu.Add("Exit", (*) => ExitApp())
    FileMenu.SetIcon("Settings...","shell32.dll", 166)

    ; Create Manage menu
    ManageMenu := Menu()
    ManageMenu.Add("Post-Exam Mode", MenuHandler)
    ManageMenu.SetIcon("Post-Exam Mode", "shell32.dll", 138)

    ; Set checkmark if post-exam mode is enabled
    if (PostExamModeEnabled)
        ManageMenu.Check("Post-Exam Mode")

    HelpMenu := Menu()
    HelpMenu.Add("User Manual", MenuHandler)
    HelpMenu.Add("About WinCBT-Biometric", MenuHandler)
    HelpMenu.SetIcon("User Manual", "shell32.dll", 23)
    HelpMenu.SetIcon("About WinCBT-Biometric", "shell32.dll", 278)

    MenuBar_Storage := MenuBar()
    MenuBar_Storage.Add("&File", FileMenu)
    MenuBar_Storage.Add("&Manage", ManageMenu)
    MenuBar_Storage.Add("&Help", HelpMenu)
    myGui.MenuBar := MenuBar_Storage

    ; Main title
    ;myGui.SetFont("s16 bold")
    ;myGui.Add("Text", "x0 y10 w1280 h30 Center", "Exam Verification System")
    ;myGui.SetFont("s10")

    ; Read company and exam information from db\config.ini
    dbConfigFile := dbPath . "\config.ini"
    companyName := IniRead(dbConfigFile, "Company", "Name", "University of Information and Technology")
    examName := IniRead(dbConfigFile, "Exam", "Name", "Computer Networks")
    centreID := IniRead(dbConfigFile, "Centre", "ID", "CNTR65434")

    ; Main title - show post-exam mode in title if enabled
    myGui.SetFont("s14 bold", "Bahnschrift")

    ; Create title with appropriate colors based on post-exam mode
    if (PostExamModeEnabled) {
        ; Post-Exam Mode: Blue background, White text
        global TitleText := myGui.Add("Text", "x440 y5 w415 h30 Center +Border +Background004080 cWhite", "Exam Verification System [Post-Exam]")
    } else {
        ; Pre-Exam Mode: Green background, Black text
        global TitleText := myGui.Add("Text", "x440 y5 w415 h30 Center +Border +Background008000 cBlack", "Exam Verification System")
    }

    ; Post-Exam Mode is indicated in the title bar and header
    myGui.SetFont("s12 bold", "Bahnschrift")

    ; Create datetime display with separate controls for date and time
    global DateText := myGui.Add("Text", "x1000 y5 w120 h30 Center +Border +BackgroundFFFFE0 c004080 +0x200", FormatTime(, "dd-MMM-yyyy"))
    global TimeText := myGui.Add("Text", "x1120 y5 w130 h30 Center +Border +BackgroundFFFFE0 cFF8000 +0x200", FormatTime(, "hh:mm:ss tt"))

    ; Set larger font for date and time
    DateText.SetFont("s14 bold", "Bahnschrift")
    TimeText.SetFont("s14 bold", "Bahnschrift")

    myGui.Add("Text", "x1000 y45 w250 h30 Center c263238", "Centre ID: " centreID)

    myGui.SetFont("s12 bold", "Bahnschrift")
    myGui.Add("Text", "x450 y40 w400 h30 Center c263238", companyName)
    myGui.Add("Text", "x450 y60 w400 h30 Center c263238", examName)

    ; Set up timer to update date/time every second
    SetTimer(UpdateDateTime, 1000)

    myGui.SetFont("s10")
    ; ================ TOP HEADER ==============================================
    ; Check if company logo exists, use gray placeholder if not
    companyLogoPath := PathManager.GetImageFilePath("CompanyLogo")
    grayPlaceholderPath := PathManager.GetImageFilePath("Gray")

    ; First check if the gray placeholder exists
    if (!FileExist(grayPlaceholderPath)) {
        ErrorHandler.LogMessage("WARNING", "Gray placeholder image not found at: " grayPlaceholderPath)
        ; Create a text control as fallback
        myGui.SetFont("s14 bold", "Bahnschrift")
        myGui.Add("Text", "x10 y5 w420 h75 +Border +Center +0x200 BackgroundE0E0E0", companyName)
        myGui.SetFont("s10")
    }
    ; Then check if company logo exists
    else if (!FileExist(companyLogoPath)) {
        ErrorHandler.LogMessage("WARNING", "Company logo not found at: " companyLogoPath)
        ; Use gray placeholder image instead
        try {
            myGui.Add("Picture", "x10 y5 w420 h75 +Border", grayPlaceholderPath)
            ErrorHandler.LogMessage("INFO", "Using gray placeholder for company logo")

            ; Add company name as text overlay
            myGui.SetFont("s14 bold", "Bahnschrift")
            myGui.Add("Text", "x10 y5 w420 h75 +Border +Center +0x200 +BackgroundTrans", companyName)
            myGui.SetFont("s10")
        } catch as err {
            ErrorHandler.LogMessage("WARNING", "Failed to load gray placeholder: " err.Message)
            ; Create a text control as fallback
            myGui.SetFont("s14 bold", "Bahnschrift")
            myGui.Add("Text", "x10 y5 w420 h75 +Border +Center +0x200 BackgroundE0E0E0", companyName)
            myGui.SetFont("s10")
        }
    }
    ; Try to load the company logo
    else {
        try {
            myGui.Add("Picture", "x10 y5 w420 h75 +Border", companyLogoPath)
            ErrorHandler.LogMessage("INFO", "Loaded company logo from: " companyLogoPath)
        } catch as err {
            ErrorHandler.LogMessage("WARNING", "Failed to load company logo: " err.Message)
            ; Use gray placeholder image as fallback
            try {
                myGui.Add("Picture", "x10 y5 w420 h75 +Border", grayPlaceholderPath)
                ErrorHandler.LogMessage("INFO", "Using gray placeholder after company logo load failure")

                ; Add company name as text overlay
                myGui.SetFont("s14 bold", "Bahnschrift")
                myGui.Add("Text", "x10 y5 w420 h75 +Border +Center +0x200 +BackgroundTrans", companyName)
                myGui.SetFont("s10")
            } catch as err2 {
                ErrorHandler.LogMessage("WARNING", "Failed to load gray placeholder: " err2.Message)
                ; Create a text control as final fallback
                myGui.SetFont("s14 bold", "Bahnschrift")
                myGui.Add("Text", "x10 y5 w420 h75 +Border +Center +0x200 BackgroundE0E0E0", companyName)
                myGui.SetFont("s10")
            }
        }
    }


    ; ================ LEFT PANEL - SEARCH & REGISTERED DETAILS ================
    myGui.Add("GroupBox", "x10 y90 w420 h545 Center", "Candidate Details")

    ; Declare all GUI controls as global within the Constructor function
    global EditRollSearch, ButtonSearch, EditNameSearch
    global CandidateNameText, FatherNameText, GenderText, DateOfBirthText, LanguageText, SpecialStatusText
    global RegisteredPhoto, RegisteredSignature, SignatureCaptureImage
    global ReviewPhotoLabel, ButtonCapturePicture, ButtonRecapturePhoto, ButtonUsePhoto
    global ButtonCaptureSignature, ButtonCaptureFingerprint, ButtonCaptureRightFingerprint
    global EnableRightThumbCheckbox, SingleThumbCheckbox, LeftThumbRadio, RightThumbRadio, SpecialCaseIndicator
    global CaptureGroupBox, VerificationGroupBox
    global VerifyPhotoImage, PhotoStatusValue, ButtonVerifyPicture
    global VerifyFingerprintImage, FingerprintStatusValue, ButtonVerifyFingerprint
    global VerifyRightFingerprintImage, RightFingerprintStatusValue, ButtonVerifyRightFingerprint
    global SignatureVerifyImage, SignatureStatusValue, ButtonVerifySignature
    global VerificationStatusValue, PostVerificationStatusValue, AssignedSeatValue, SeatDetailsText, ButtonAssignSeat

    ; Search Controls
    myGui.Add("Text", "x30 y120 w130 h23", "Search:")
    EditRollSearch := myGui.Add("Edit", "x170 y120 w180 h23", "9351")
    ButtonSearch := myGui.Add("Button", "x360 y120 w50 h23 +Default +0x1000", "Go")
    myGui.Add("Text", "x30 y150 w130 h23", "Search Status:")
    EditNameSearch := myGui.Add("Edit", "x170 y150 w240 h23 +ReadOnly")

    ; Registered Candidate Info
    myGui.SetFont("s12 q5 cTeal", "Gill Sans Nova Bold")
    myGui.Add("Text", "x20 y185 w400 h23 +Border Center", "Candidate Information")
    myGui.SetFont("s11 q5 cBlack", "Gill Sans Nova")
    myGui.Add("Text", "x30 y220 w100 h23 Right", "Name:")
    CandidateNameText := myGui.Add("Text", "x140 y220 w270 h23", "-")
    myGui.Add("Text", "x30 y245 w100 h23 Right", "Father Name:")
    FatherNameText := myGui.Add("Text", "x140 y245 w270 h23", "-")
    myGui.Add("Text", "x30 y270 w100 h23 Right", "Gender:")
    GenderText := myGui.Add("Text", "x140 y270 w270 h23", "-")
    myGui.Add("Text", "x30 y295 w100 h23 Right", "Date of Birth:")
    DateOfBirthText := myGui.Add("Text", "x140 y295 w270 h23", "-")
    myGui.Add("Text", "x30 y320 w100 h23 Right", "Language:")
    LanguageText := myGui.Add("Text", "x140 y320 w270 h23", "-")
    myGui.Add("Text", "x30 y345 w100 h23 Right", "Special Status:")
    SpecialStatusText := myGui.Add("Text", "x140 y345 w270 h23", "-")

    myGui.SetFont("s10")

    ; Get default image paths with fallbacks using PathManager
    defaultPhotoPath := PathManager.GetImageFilePath("DefaultPhoto")
    grayPlaceholderPath := PathManager.GetImageFilePath("Gray")

    ; Check if default photo exists, use gray placeholder if not
    if (!FileExist(defaultPhotoPath)) {
        ErrorHandler.LogMessage("WARNING", "Default photo image not found at: " defaultPhotoPath)
        defaultPhotoPath := grayPlaceholderPath
    }

    ; Check if gray placeholder exists, create a fallback if not
    if (!FileExist(grayPlaceholderPath)) {
        ErrorHandler.LogMessage("WARNING", "Gray placeholder image not found at: " grayPlaceholderPath)
        ; We'll handle this in the individual image loading sections
    }

    ; Registered Photo with error handling
    try {
        RegisteredPhoto := myGui.Add("Picture", "x20 y380 w210 h225 +Border", defaultPhotoPath)
        myGui.Add("Text", "x20 y610 w210 h23 Center", "Registered Photo")
    } catch as err {
        ErrorHandler.LogMessage("WARNING", "Failed to load default photo: " err.Message)
        ; Create a text control as fallback
        myGui.Add("Text", "x20 y380 w210 h225 +Border +Center +0x200 BackgroundE0E0E0", "No Photo")
        myGui.Add("Text", "x20 y610 w210 h23 Center", "Registered Photo")
    }

    ; Registered Signature with error handling
    try {
        RegisteredSignature := myGui.Add("Picture", "x240 y380 w180 h100 +Border", grayPlaceholderPath)
        myGui.Add("Text", "x240 y485 w180 h23 Center", "Registered Signature")
    } catch as err {
        ErrorHandler.LogMessage("WARNING", "Failed to load gray placeholder for signature: " err.Message)
        ; Create a text control as fallback
        myGui.Add("Text", "x240 y380 w180 h100 +Border +Center +0x200 BackgroundE0E0E0", "No Signature")
        myGui.Add("Text", "x240 y485 w180 h23 Center", "Registered Signature")
    }

    ; Captured Signature with error handling
    try {
        SignatureCaptureImage := myGui.Add("Picture", "x240 y505 w180 h100 +Border", grayPlaceholderPath)
        myGui.Add("Text", "x240 y610 w180 h23 Center", "Captured Signature")
    } catch as err {
        ErrorHandler.LogMessage("WARNING", "Failed to load gray placeholder for captured signature: " err.Message)
        ; Create a text control as fallback
        myGui.Add("Text", "x240 y505 w180 h100 +Border +Center +0x200 BackgroundE0E0E0", "No Signature")
        myGui.Add("Text", "x240 y610 w180 h23 Center", "Captured Signature")
    }

    ; ================ MIDDLE PANEL - LIVE CAPTURE & VERIFICATION ================
    CaptureGroupBox := myGui.Add("GroupBox", "x440 y90 w420 h545 Center", "Capture")

    ; Note: We're using the webcam controls created earlier in the constructor
    ; The webcamControl (Text) and capturedImageControl (Picture) are already set up
    ; and the webcam is already initialized

    ; Create a reference to the webcam controls for backward compatibility
    global WebcamFeed := capturedImageControl

    ErrorHandler.LogMessage("INFO", "Using webcam controls initialized in constructor")

    ; Add other webcam-related controls with error handling
    try {
        ReviewPhotoLabel := myGui.Add("Text", "x480 y455 w340 h23 Center Hidden", "Review captured photo")
        ButtonCapturePicture := myGui.Add("Button", "x560 y460 w180 h50 Disabled +0x1000", "Capture Photo")
        ButtonRecapturePhoto := myGui.Add("Button", "x480 y480 w160 h30 Hidden +0x1000", "Recapture")
        ButtonUsePhoto := myGui.Add("Button", "x660 y480 w160 h30 Hidden +0x1000", "Use This Photo")
    } catch as err {
        ErrorHandler.LogMessage("ERROR", "Failed to create webcam control buttons: " err.Message)
        ; Create dummy objects to prevent null reference errors
        ReviewPhotoLabel := {Visible: false}
        ButtonCapturePicture := {Enabled: false, Text: ""}
        ButtonRecapturePhoto := {Visible: false}
        ButtonUsePhoto := {Visible: false}
    }

    ; ThumbPrint and Signature - Swapped positions and moved upward
    ButtonCaptureSignature := myGui.Add("Button", "x450 y525 w120 h60 Disabled +0x1000 +Border", "Capture Signature")
    ButtonCaptureFingerprint := myGui.Add("Button", "x590 y525 w120 h60 Disabled +0x1000 +Border", "Capture ThumbPrint  (Left)")
    ButtonCaptureRightFingerprint := myGui.Add("Button", "x730 y525 w120 h60 Disabled +0x1000 +Border", "Capture ThumbPrint (Right)")

    ; Thumbprint exception handling controls
    myGui.SetFont("s8")
    EnableRightThumbCheckbox := myGui.Add("Checkbox", "x730 y610 w120 h20 Disabled Hidden", "Use Right Thumb")
    SingleThumbCheckbox := myGui.Add("Checkbox", "x450 y610 w160 h20 Disabled Hidden", "Single Thumb Available")
    LeftThumbRadio := myGui.Add("Radio", "x620 y590 w80 h20 Disabled Hidden Group", "Left Thumb")
    RightThumbRadio := myGui.Add("Radio", "x730 y590 w90 h20 Disabled Hidden", "Right Thumb")
    SpecialCaseIndicator := myGui.Add("Text", "x450 y590 w120 h20 cRed Hidden", "Special Case")
    myGui.SetFont("s10")
    if (!SignatureVerificationEnabled) {
        SignatureCaptureImage.Value := "img\gray.png"
        SignatureCaptureImage.Opt("Disabled")
        ButtonCaptureSignature.Enabled := false
    }

    ; ================ RIGHT PANEL - STATUS & ACTIONS ================
    VerificationGroupBox := myGui.Add("GroupBox", "x870 y90 w400 h545", "Verification")

    ; Post-Exam Mode is indicated in the title bar and header
    myGui.SetFont("s10")

    ; Verification Status with error handling
    ; Get default fingerprint image path using PathManager
    defaultFingerprintPath := PathManager.GetImageFilePath("DefaultFingerprint")

    ; Check if default fingerprint image exists, use gray placeholder if not
    if (!FileExist(defaultFingerprintPath)) {
        ErrorHandler.LogMessage("WARNING", "Default fingerprint image not found at: " defaultFingerprintPath)
        defaultFingerprintPath := grayPlaceholderPath
    }

    ; Add verification photo image with error handling
    try {
        VerifyPhotoImage := myGui.Add("Picture", "x890 y120 w100 h80 +Border", defaultPhotoPath)
        myGui.Add("Text", "x1000 y120 w120 h23", "Photo:")
        PhotoStatusValue := myGui.Add("Text", "x1120 y120 w120 h23 cGray", "-")
        ButtonVerifyPicture := myGui.Add("Button", "x1000 y150 w180 h30 Disabled +0x1000", "Verify Photo")
    } catch as err {
        ErrorHandler.LogMessage("WARNING", "Failed to create photo verification controls: " err.Message)
        ; Create text controls as fallback
        myGui.Add("Text", "x890 y120 w100 h80 +Border +Center +0x200 BackgroundE0E0E0", "No Photo")
        myGui.Add("Text", "x1000 y120 w120 h23", "Photo:")
        PhotoStatusValue := myGui.Add("Text", "x1120 y120 w120 h23 cGray", "-")
        ButtonVerifyPicture := myGui.Add("Button", "x1000 y150 w180 h30 Disabled +0x1000", "Verify Photo")
    }

    ; Add left fingerprint verification controls with error handling
    try {
        VerifyFingerprintImage := myGui.Add("Picture", "x890 y220 w100 h80 +Border", defaultFingerprintPath)
        myGui.Add("Text", "x1000 y220 w120 h23", "ThumbPrint Left:")
        FingerprintStatusValue := myGui.Add("Text", "x1120 y220 w120 h23 cGray", "-")
        if (FingerprintMode == "save") {
            ButtonVerifyFingerprint := myGui.Add("Button", "x1000 y250 w180 h30 Disabled +0x1000", "Save ThumbPrint (Left)")
        } else {
            ButtonVerifyFingerprint := myGui.Add("Button", "x1000 y250 w180 h30 Disabled +0x1000", "Verify ThumbPrint (Left)")
        }
    } catch as err {
        ErrorHandler.LogMessage("WARNING", "Failed to create left fingerprint verification controls: " err.Message)
        ; Create text controls as fallback
        myGui.Add("Text", "x890 y220 w100 h80 +Border +Center +0x200 BackgroundE0E0E0", "No Print")
        myGui.Add("Text", "x1000 y220 w120 h23", "ThumbPrint Left:")
        FingerprintStatusValue := myGui.Add("Text", "x1120 y220 w120 h23 cGray", "-")
        if (FingerprintMode == "save") {
            ButtonVerifyFingerprint := myGui.Add("Button", "x1000 y250 w180 h30 Disabled +0x1000", "Save ThumbPrint (Left)")
        } else {
            ButtonVerifyFingerprint := myGui.Add("Button", "x1000 y250 w180 h30 Disabled +0x1000", "Verify ThumbPrint (Left)")
        }
    }

    ; Add right fingerprint verification controls with error handling
    try {
        VerifyRightFingerprintImage := myGui.Add("Picture", "x890 y320 w100 h80 +Border", defaultFingerprintPath)
        myGui.Add("Text", "x1000 y320 w150 h23", "ThumbPrint Right:")
        RightFingerprintStatusValue := myGui.Add("Text", "x1120 y320 w120 h23 cGray", "-")
        if (FingerprintMode == "save") {
            ButtonVerifyRightFingerprint := myGui.Add("Button", "x1000 y350 w180 h30 Disabled +0x1000", "Save ThumbPrint (Right)")
        } else {
            ButtonVerifyRightFingerprint := myGui.Add("Button", "x1000 y350 w180 h30 Disabled +0x1000", "Verify ThumbPrint (Right)")
        }
    } catch as err {
        ErrorHandler.LogMessage("WARNING", "Failed to create right fingerprint verification controls: " err.Message)
        ; Create text controls as fallback
        myGui.Add("Text", "x890 y320 w100 h80 +Border +Center +0x200 BackgroundE0E0E0", "No Print")
        myGui.Add("Text", "x1000 y320 w150 h23", "ThumbPrint Right:")
        RightFingerprintStatusValue := myGui.Add("Text", "x1120 y320 w120 h23 cGray", "-")
        if (FingerprintMode == "save") {
            ButtonVerifyRightFingerprint := myGui.Add("Button", "x1000 y350 w180 h30 Disabled +0x1000", "Save ThumbPrint (Right)")
        } else {
            ButtonVerifyRightFingerprint := myGui.Add("Button", "x1000 y350 w180 h30 Disabled +0x1000", "Verify ThumbPrint (Right)")
        }
    }

    ; Signature verification controls with error handling
    ; Get default signature image path using PathManager
    defaultSignaturePath := PathManager.GetImageFilePath("DefaultSignature")

    ; Check if default signature image exists, use gray placeholder if not
    if (!FileExist(defaultSignaturePath)) {
        ErrorHandler.LogMessage("WARNING", "Default signature image not found at: " defaultSignaturePath)
        defaultSignaturePath := grayPlaceholderPath
    }

    ; Add signature verification controls with error handling
    try {
        SignatureVerifyImage := myGui.Add("Picture", "x890 y420 w100 h80 +Border", defaultSignaturePath)
        myGui.Add("Text", "x1000 y420 w120 h23", "Signature:")
        SignatureStatusValue := myGui.Add("Text", "x1120 y420 w120 h23 cGray", "-")
        ButtonVerifySignature := myGui.Add("Button", "x1000 y450 w180 h30 Disabled +0x1000", "Verify Signature")

        ; Handle disabled signature verification
        if (!SignatureVerificationEnabled) {
            try {
                SignatureVerifyImage.Value := grayPlaceholderPath
                SignatureVerifyImage.Opt("Disabled")
                SignatureStatusValue.Text := "Disabled"
                SignatureStatusValue.Opt("cGray")
                ButtonVerifySignature.Enabled := false
                ButtonVerifySignature.Text := "Disabled"
                ErrorHandler.LogMessage("INFO", "Initial setup: Signature verification disabled - status set to Disabled")
            } catch as err {
                ErrorHandler.LogMessage("WARNING", "Failed to update signature controls for disabled state: " err.Message)
            }
        }
    } catch as err {
        ErrorHandler.LogMessage("WARNING", "Failed to create signature verification controls: " err.Message)
        ; Create text controls as fallback
        myGui.Add("Text", "x890 y420 w100 h80 +Border +Center +0x200 BackgroundE0E0E0", "No Signature")
        myGui.Add("Text", "x1000 y420 w120 h23", "Signature:")
        SignatureStatusValue := myGui.Add("Text", "x1120 y420 w120 h23 cGray", "Disabled")
        ButtonVerifySignature := myGui.Add("Button", "x1000 y450 w180 h30 Disabled +0x1000", "Disabled")
    }

    ; Separator
    ; myGui.Add("Text", "x880 y460 w380 h1 +0x10") ; manually removed

    ; Overall Status
    myGui.SetFont("s10 bold")
    ;myGui.Add("Text", "x890 y480 w380 h23", "Results") ; manually removed
    myGui.SetFont("s10")
    myGui.Add("Text", "x890 y520 w130 h23", "Pre Verification:")
    VerificationStatusValue := myGui.Add("Text", "x1010 y520 w100 h23 cGray", "-")
    myGui.Add("Text", "x890 y550 w120 h23", "Assigned Seat:")
    AssignedSeatValue := myGui.Add("Text", "x1010 y550 w100 h23", "-")
    myGui.Add("Text", "x890 y580 w130 h23", "Post Verification:")
    global PostVerificationStatusValue := myGui.Add("Text", "x1010 y580 w100 h23 cGray", "-")
    global SeatDetailsText := myGui.Add("Text", "x890 y610 w250 h23 cGreen Hidden", "Seat: 5, Room: 12, Floor: 2")
    ButtonAssignSeat := myGui.Add("Button", "x1140 y520 w120 h80 Disabled +0x1000", "Assign Seat")

    ; ========= FOOTER ======================================
    ; Get real IP and MAC address
    ipAddress := GetLocalIP()
    macAddress := GetMACAddress()

    ; Ensure global variables are accessible and have default values if not set
    global g_fingerprintReaderStatus, g_fingerprintDeviceSN, g_cameraStatus, RightThumbprintVerificationEnabled

    ; Set default values if not already set
    if (!IsSet(g_fingerprintReaderStatus) || g_fingerprintReaderStatus = "")
        g_fingerprintReaderStatus := "Not Connected"

    if (!IsSet(g_fingerprintDeviceSN) || g_fingerprintDeviceSN = "")
        g_fingerprintDeviceSN := ""

    if (!IsSet(g_cameraStatus) || g_cameraStatus = "")
        g_cameraStatus := "Not Connected"

    if (!IsSet(RightThumbprintVerificationEnabled))
        RightThumbprintVerificationEnabled := 1  ; Default to dual thumb mode

    myGui.SetFont("s11", "Bahnschrift")
    global FooterText := myGui.Add("Text", "x10 y640 w1260 h30 +Border -BackgroundGray cBlack +0x200",
                                  " IP: " . ipAddress .
                                  " | MAC: " . macAddress .
                                  " | Camera: " . g_cameraStatus .
                                  " | Fingerprint: " . g_fingerprintReaderStatus .
                                  " | Thumbprint: " . (RightThumbprintVerificationEnabled ? "Dual Thumb" : "Single Thumb") .
                                  " | Operator1 (Logged In)")

    ; Perform one-time device status check and update the footer
    CheckDeviceStatus()

    ; Ensure title bar is properly set based on current mode
    UpdateTitleBar()

    myGui.SetFont("s10")

    ; Initially disable the capture and verification sections
    CaptureGroupBox.Opt("Disabled")
    VerificationGroupBox.Opt("Disabled")

    ; ; UpdateButtonStatesBasedOnVerificationStatus()
    ; ; Manages button states based on current verification status
    ; ; Disables capture and verification buttons when verification is complete
    ; ; while preserving re-verification functionality in the verification panel
    UpdateButtonStatesBasedOnVerificationStatus() {
        try {
            ; Declare all button controls as global
            global ButtonCapturePicture, ButtonRecapturePhoto, ButtonVerifyPicture
            global ButtonCaptureFingerprint, ButtonVerifyFingerprint
            global ButtonCaptureRightFingerprint, ButtonVerifyRightFingerprint
            global PhotoStatusValue, FingerprintStatusValue, RightFingerprintStatusValue

            OutputDebug("UpdateButtonStatesBasedOnVerificationStatus: Starting button state update")

            ; === Photo Verification Button Management ===
            if (IsSet(PhotoStatusValue) && IsObject(PhotoStatusValue)) {
                photoStatus := PhotoStatusValue.Text
                OutputDebug("UpdateButtonStatesBasedOnVerificationStatus: Photo status = " photoStatus)

                ; Disable main capture button when photo is verified
                if (photoStatus == "Verified") {
                    if (IsSet(ButtonCapturePicture) && IsObject(ButtonCapturePicture)) {
                        ButtonCapturePicture.Enabled := false
                        OutputDebug("UpdateButtonStatesBasedOnVerificationStatus: Disabled main photo capture button (status: Verified)")
                    }
                    ; Note: ButtonVerifyPicture in verification panel remains enabled for re-verification
                    ; Note: ButtonRecapturePhoto in review section is separate and handled by review logic
                }
            }

            ; === Left Fingerprint Verification Button Management ===
            if (IsSet(FingerprintStatusValue) && IsObject(FingerprintStatusValue)) {
                leftFingerprintStatus := FingerprintStatusValue.Text
                OutputDebug("UpdateButtonStatesBasedOnVerificationStatus: Left fingerprint status = " leftFingerprintStatus)

                ; Disable buttons when left fingerprint is saved
                if (leftFingerprintStatus == "Saved") {
                    ; Disable capture button
                    if (IsSet(ButtonCaptureFingerprint) && IsObject(ButtonCaptureFingerprint)) {
                        ButtonCaptureFingerprint.Enabled := false
                        OutputDebug("UpdateButtonStatesBasedOnVerificationStatus: Disabled left fingerprint capture button (status: Saved)")
                    }
                    ; Disable verification button
                    if (IsSet(ButtonVerifyFingerprint) && IsObject(ButtonVerifyFingerprint)) {
                        ButtonVerifyFingerprint.Enabled := false
                        OutputDebug("UpdateButtonStatesBasedOnVerificationStatus: Disabled left fingerprint verification button (status: Saved)")
                    }
                }
            }

            ; === Right Fingerprint Verification Button Management ===
            if (IsSet(RightFingerprintStatusValue) && IsObject(RightFingerprintStatusValue)) {
                rightFingerprintStatus := RightFingerprintStatusValue.Text
                OutputDebug("UpdateButtonStatesBasedOnVerificationStatus: Right fingerprint status = " rightFingerprintStatus)

                ; Disable buttons when right fingerprint is saved
                if (rightFingerprintStatus == "Saved") {
                    ; Disable capture button
                    if (IsSet(ButtonCaptureRightFingerprint) && IsObject(ButtonCaptureRightFingerprint)) {
                        ButtonCaptureRightFingerprint.Enabled := false
                        OutputDebug("UpdateButtonStatesBasedOnVerificationStatus: Disabled right fingerprint capture button (status: Saved)")
                    }
                    ; Disable verification button
                    if (IsSet(ButtonVerifyRightFingerprint) && IsObject(ButtonVerifyRightFingerprint)) {
                        ButtonVerifyRightFingerprint.Enabled := false
                        OutputDebug("UpdateButtonStatesBasedOnVerificationStatus: Disabled right fingerprint verification button (status: Saved)")
                    }
                }
            }

            OutputDebug("UpdateButtonStatesBasedOnVerificationStatus: Button state update completed")

        } catch as err {
            OutputDebug("UpdateButtonStatesBasedOnVerificationStatus: Error updating button states: " err.Message)
        }
    }

    ; Helper function to check if all verifications are complete
    ; ; CheckAllVerifications()
    ; ; Evaluates the status of photo, fingerprint, and signature verification
    ; ; based on current GUI control values and configuration settings (e.g., FingerprintMode).
    ; ; Updates the overall VerificationStatusValue and enables/disables the AssignSeat button accordingly.
    CheckAllVerifications() {
        ; Debug logging
        debugStatus := "Photo: " PhotoStatusValue.Text ", Fingerprint: " FingerprintStatusValue.Text
        debugStatus .= ", Mode: " FingerprintMode ", Signature: " SignatureStatusValue.Text
        debugStatus .= ", Right Fingerprint: " RightFingerprintStatusValue.Text
        OutputDebug(debugStatus)

        ; Update button states based on verification status
        UpdateButtonStatesBasedOnVerificationStatus()

        ; Initialize verified status
        photoVerified := (PhotoStatusValue.Text == "Verified")

        ; Left Fingerprint verification depends on mode
        leftFingerprintVerified := false
        if (FingerprintMode == "save") {
            leftFingerprintVerified := (FingerprintStatusValue.Text == "Saved")
            OutputDebug("Save mode: Left Fingerprint verified = " leftFingerprintVerified)
        } else {
            leftFingerprintVerified := (FingerprintStatusValue.Text == "Verified" || FingerprintStatusValue.Text == "Skipped")
            ; Note: "Ready (High Quality)" is not considered verified yet - it still needs to be compared
            OutputDebug("Compare mode: Left Fingerprint verified = " leftFingerprintVerified)
        }

        ; Check if right thumb is enabled for this candidate (regardless of global setting)
        rightThumbEnabled := EnableRightThumbCheckbox.Value
        singleThumbMode := SingleThumbCheckbox.Value

        ; Get the current candidate's roll number
        rollNumber := EditRollSearch.Value

        ; Check if this is a special accommodation candidate
        isSpecialCandidate := false

        ; Get database paths for reading Special flag
        if (SubStr(dbPath, 1, 1) != "\" && SubStr(dbPath, 2, 1) != ":") {
            dbPathFull := A_ScriptDir "\" dbPath
        } else {
            dbPathFull := dbPath
        }

        ; Define database file path
        candidatesPath := dbPathFull "\candidates.ini"

        ; Read the special flag
        try {
            specialFlag := IniRead(candidatesPath, rollNumber, "Special", "0")
            isSpecialCandidate := (specialFlag == "1")
            if (isSpecialCandidate)
                OutputDebug("Special candidate detected in CheckAllVerifications")
        } catch Error as e {
            OutputDebug("Error reading special flag: " e.Message)
        }

        ; Handle special cases based on UI state, not database flags
        if (rightThumbEnabled && !singleThumbMode) {
            ; If right thumb checkbox is checked (and not in single thumb mode),
            ; left thumb is automatically considered verified
            leftFingerprintVerified := true
            OutputDebug("Right thumb enabled for this candidate, automatically considering left thumb verified")

            ; Check right thumb verification status
            if (FingerprintMode == "save") {
                rightFingerprintVerified := (RightFingerprintStatusValue.Text == "Saved")
                OutputDebug("Save mode: Right Fingerprint verified = " rightFingerprintVerified)
            } else {
                rightFingerprintVerified := (RightFingerprintStatusValue.Text == "Verified" || RightFingerprintStatusValue.Text == "Skipped")
                OutputDebug("Compare mode: Right Fingerprint verified = " rightFingerprintVerified)
            }
        }
        else if (singleThumbMode) {
            ; In single thumb mode, check which thumb is selected
            if (LeftThumbRadio.Value) {
                ; Left thumb selected, right thumb is automatically considered verified
                rightFingerprintVerified := true
                OutputDebug("Single thumb mode with left thumb selected, automatically considering right thumb verified")

                ; Check left thumb verification status
                if (FingerprintMode == "save") {
                    leftFingerprintVerified := (FingerprintStatusValue.Text == "Saved")
                    OutputDebug("Save mode: Left Fingerprint verified = " leftFingerprintVerified)
                } else {
                    leftFingerprintVerified := (FingerprintStatusValue.Text == "Verified" || FingerprintStatusValue.Text == "Skipped")
                    OutputDebug("Compare mode: Left Fingerprint verified = " leftFingerprintVerified)
                }
            } else if (RightThumbRadio.Value) {
                ; Right thumb selected, left thumb is automatically considered verified
                leftFingerprintVerified := true
                OutputDebug("Single thumb mode with right thumb selected, automatically considering left thumb verified")

                ; Check right thumb verification status
                if (FingerprintMode == "save") {
                    rightFingerprintVerified := (RightFingerprintStatusValue.Text == "Saved")
                    OutputDebug("Save mode: Right Fingerprint verified = " rightFingerprintVerified)
                } else {
                    rightFingerprintVerified := (RightFingerprintStatusValue.Text == "Verified" || RightFingerprintStatusValue.Text == "Skipped")
                    OutputDebug("Compare mode: Right Fingerprint verified = " rightFingerprintVerified)
                }
            } else {
                ; No thumb selected, neither is verified
                leftFingerprintVerified := false
                rightFingerprintVerified := false
                OutputDebug("Single thumb mode but no thumb selected, neither thumb is verified")
            }
        }
        else if (isSpecialCandidate) {
            ; Special candidate handling
            ; Check if left thumb is enabled
            if (ButtonCaptureFingerprint.Enabled) {
                ; Check left thumb verification status
                if (FingerprintMode == "save") {
                    leftFingerprintVerified := (FingerprintStatusValue.Text == "Saved")
                    OutputDebug("Special candidate - Save mode: Left Fingerprint verified = " leftFingerprintVerified)
                } else {
                    leftFingerprintVerified := (FingerprintStatusValue.Text == "Verified" || FingerprintStatusValue.Text == "Skipped")
                    OutputDebug("Special candidate - Compare mode: Left Fingerprint verified = " leftFingerprintVerified)
                }
            } else {
                ; Left thumb is disabled, so it's automatically considered verified
                leftFingerprintVerified := true
                OutputDebug("Special candidate: Left thumb disabled, automatically considering it verified")
            }

            ; Check if right thumb is enabled
            if (ButtonCaptureRightFingerprint.Enabled) {
                ; Check right thumb verification status
                if (FingerprintMode == "save") {
                    rightFingerprintVerified := (RightFingerprintStatusValue.Text == "Saved")
                    OutputDebug("Special candidate - Save mode: Right Fingerprint verified = " rightFingerprintVerified)
                } else {
                    rightFingerprintVerified := (RightFingerprintStatusValue.Text == "Verified" || RightFingerprintStatusValue.Text == "Skipped")
                    OutputDebug("Special candidate - Compare mode: Right Fingerprint verified = " rightFingerprintVerified)
                }
            } else {
                ; Right thumb is disabled, so it's automatically considered verified
                rightFingerprintVerified := true
                OutputDebug("Special candidate: Right thumb disabled, automatically considering it verified")
            }
        } else {
            ; Standard case - no special accommodations
            ; If right thumb is not enabled for this candidate
            if (RightThumbprintVerificationEnabled) {
                ; When global setting is enabled but checkbox is not checked,
                ; we need to verify both fingerprints
                rightFingerprintVerified := (RightFingerprintStatusValue.Text == "Verified" ||
                                           RightFingerprintStatusValue.Text == "Saved" ||
                                           RightFingerprintStatusValue.Text == "Skipped")
                OutputDebug("Right thumb verification enabled globally but not checked for this candidate: " rightFingerprintVerified)
            } else {
                ; If right thumbprint verification is disabled globally and checkbox is not checked,
                ; it's automatically considered verified
                rightFingerprintVerified := true
                OutputDebug("Right thumbprint verification is disabled globally, automatically considering it verified")
            }
        }

        ; Both fingerprints need to be verified based on their respective requirements
        fingerprintVerified := leftFingerprintVerified && rightFingerprintVerified

        ; If signature verification is disabled, we don't consider it in the verification process
        ; If it's enabled, then it needs to be verified
        signatureVerified := (!SignatureVerificationEnabled || SignatureStatusValue.Text == "Verified")

        ; Debug output for signature verification
        if (!SignatureVerificationEnabled) {
            OutputDebug("Signature verification is disabled, automatically considering it verified")
        }

        ; Debug output for verification states
        OutputDebug("Verification states - Photo: " photoVerified ", Fingerprint: " fingerprintVerified ", Signature: " signatureVerified)

        ; Get database paths for updating BiometricStatus
        rollNumber := EditRollSearch.Value

        ; Get database file path using PathManager
        candidatesPath := PathManager.GetDatabaseFilePath("Candidates")

        ; Check if all required verifications are complete
        if (photoVerified && fingerprintVerified && signatureVerified) {
            ; Update the appropriate status field based on mode
            if (g_isPostExamMode) {
                PostVerificationStatusValue.Text := "Completed"
                PostVerificationStatusValue.Opt("cGreen")
                OutputDebug("All post-exam verifications complete")
            } else {
                VerificationStatusValue.Text := "Completed"
                VerificationStatusValue.Opt("cGreen")
                OutputDebug("All pre-exam verifications complete")
            }

            ; Only show and enable seat assignment button if not in post-exam mode
            if (!g_isPostExamMode) {
                ButtonAssignSeat.Enabled := true
                ButtonAssignSeat.Visible := true
                OutputDebug("All verifications complete, enabling seat assignment")
            } else {
                ButtonAssignSeat.Visible := false
                OutputDebug("All post-exam verifications complete, seat assignment button hidden")
            }

            ; Update BiometricStatus to Verified in the database
            try {
                ; Use appropriate status fields based on mode
                if (g_isPostExamMode) {
                    ; Update post-exam status fields
                    IniWrite("Verified", candidatesPath, rollNumber, "PostExamBiometricStatus")
                    OutputDebug("Updated PostExamBiometricStatus to Verified in database")

                    ; Also update individual verification statuses if they're not already set
                    if (photoVerified && IniRead(candidatesPath, rollNumber, "PostExamPhotoStatus", "") != "Verified") {
                        IniWrite("Verified", candidatesPath, rollNumber, "PostExamPhotoStatus")
                        OutputDebug("Updated PostExamPhotoStatus to Verified in database")
                    }

                    ; Handle fingerprint status updates based on verification mode
                    if (rightThumbEnabled && !singleThumbMode) {
                        ; Both thumbs are required
                        if (fingerprintVerified && IniRead(candidatesPath, rollNumber, "PostExamFingerprintStatus", "") != "Verified") {
                            IniWrite("Verified", candidatesPath, rollNumber, "PostExamFingerprintStatus")
                            OutputDebug("Updated PostExamFingerprintStatus to Verified in database")
                        }

                        if (rightFingerprintVerified && IniRead(candidatesPath, rollNumber, "PostExamRightFingerprintStatus", "") != "Verified") {
                            IniWrite("Verified", candidatesPath, rollNumber, "PostExamRightFingerprintStatus")
                            OutputDebug("Updated PostExamRightFingerprintStatus to Verified in database")
                        }
                    } else if (singleThumbMode) {
                        ; Single thumb mode - check which thumb is selected
                        if (LeftThumbRadio.Value) {
                            if (leftFingerprintVerified && IniRead(candidatesPath, rollNumber, "PostExamFingerprintStatus", "") != "Verified") {
                                IniWrite("Verified", candidatesPath, rollNumber, "PostExamFingerprintStatus")
                                OutputDebug("Updated PostExamFingerprintStatus to Verified in database (single thumb mode - left)")
                            }
                            ; Mark right as verified automatically in single thumb mode
                            IniWrite("Verified", candidatesPath, rollNumber, "PostExamRightFingerprintStatus")
                            OutputDebug("Updated PostExamRightFingerprintStatus to Verified in database (single thumb mode - auto)")
                        } else if (RightThumbRadio.Value) {
                            if (rightFingerprintVerified && IniRead(candidatesPath, rollNumber, "PostExamRightFingerprintStatus", "") != "Verified") {
                                IniWrite("Verified", candidatesPath, rollNumber, "PostExamRightFingerprintStatus")
                                OutputDebug("Updated PostExamRightFingerprintStatus to Verified in database (single thumb mode - right)")
                            }
                            ; Mark left as verified automatically in single thumb mode
                            IniWrite("Verified", candidatesPath, rollNumber, "PostExamFingerprintStatus")
                            OutputDebug("Updated PostExamFingerprintStatus to Verified in database (single thumb mode - auto)")
                        }
                    } else {
                        ; Standard mode - determine which fingerprint status to update based on checkbox
                        isRightThumb := EnableRightThumbCheckbox.Value
                        if (isRightThumb) {
                            if (fingerprintVerified && IniRead(candidatesPath, rollNumber, "PostExamRightFingerprintStatus", "") != "Verified") {
                                IniWrite("Verified", candidatesPath, rollNumber, "PostExamRightFingerprintStatus")
                                OutputDebug("Updated PostExamRightFingerprintStatus to Verified in database")
                            }
                            ; Mark left as verified automatically when right thumb is used
                            IniWrite("Verified", candidatesPath, rollNumber, "PostExamFingerprintStatus")
                            OutputDebug("Updated PostExamFingerprintStatus to Verified in database (auto when right thumb used)")
                        } else {
                            if (fingerprintVerified && IniRead(candidatesPath, rollNumber, "PostExamFingerprintStatus", "") != "Verified") {
                                IniWrite("Verified", candidatesPath, rollNumber, "PostExamFingerprintStatus")
                                OutputDebug("Updated PostExamFingerprintStatus to Verified in database")
                            }

                            ; If right thumbprint verification is not enabled, mark it as verified automatically
                            if (!RightThumbprintVerificationEnabled) {
                                IniWrite("Verified", candidatesPath, rollNumber, "PostExamRightFingerprintStatus")
                                OutputDebug("Updated PostExamRightFingerprintStatus to Verified in database (auto when disabled)")
                            }
                        }
                    }

                    if (signatureVerified && SignatureVerificationEnabled &&
                        IniRead(candidatesPath, rollNumber, "PostExamSignatureStatus", "") != "Verified") {
                        IniWrite("Verified", candidatesPath, rollNumber, "PostExamSignatureStatus")
                        OutputDebug("Updated PostExamSignatureStatus to Verified in database")
                    }

                    ; Call the post-exam verification check function to ensure consistency
                    try {
                        if (IsSet(CheckPostExamVerifications)) {
                            CheckPostExamVerifications(rollNumber)
                            OutputDebug("Called CheckPostExamVerifications to ensure consistency")
                        } else {
                            OutputDebug("CheckPostExamVerifications function not available")
                        }
                    } catch as err {
                        OutputDebug("Error calling CheckPostExamVerifications: " err.Message)
                    }

                    ; Log post-exam verification completion to application log
                    candidateName := IniRead(candidatesPath, rollNumber, "Name", "Unknown")
                    LogPostExamVerificationCompletion(rollNumber, candidateName)
                } else {
                    ; Update regular status fields
                    IniWrite("Verified", candidatesPath, rollNumber, "BiometricStatus")
                    OutputDebug("Updated BiometricStatus to Verified in database")

                    ; Also update individual verification statuses if they're not already set
                    if (photoVerified && IniRead(candidatesPath, rollNumber, "PhotoStatus", "") != "Verified") {
                        IniWrite("Verified", candidatesPath, rollNumber, "PhotoStatus")
                        OutputDebug("Updated PhotoStatus to Verified in database")
                    }

                    if (fingerprintVerified && IniRead(candidatesPath, rollNumber, "FingerprintStatus", "") != "Verified") {
                        IniWrite("Verified", candidatesPath, rollNumber, "FingerprintStatus")
                        OutputDebug("Updated FingerprintStatus to Verified in database")
                    }

                    if (signatureVerified && SignatureVerificationEnabled &&
                        IniRead(candidatesPath, rollNumber, "SignatureStatus", "") != "Verified") {
                        IniWrite("Verified", candidatesPath, rollNumber, "SignatureStatus")
                        OutputDebug("Updated SignatureStatus to Verified in database")
                    }
                }
            } catch Error as e {
                OutputDebug("Error updating verification status in database: " e.Message)
            }
        } else {
            ; Update the appropriate status field based on mode
            if (g_isPostExamMode) {
                PostVerificationStatusValue.Text := "Incomplete"
                PostVerificationStatusValue.Opt("cRed")
                OutputDebug("Post-exam verifications incomplete")
            } else {
                VerificationStatusValue.Text := "Incomplete"
                VerificationStatusValue.Opt("cRed")
                OutputDebug("Pre-exam verifications incomplete")
            }

            ButtonAssignSeat.Enabled := false
            OutputDebug("Verifications incomplete")
        }
    }

    ; Define the OnEventHandler function with actual implementation
    ; ; OnEventHandler(...)
    ; ; Centralized event handler for various GUI control interactions (clicks, changes).
    ; ; Routes actions based on the control that triggered the event (e.g., search, capture, verify).
    ; ; @param GuiCtrlObj: The GuiControl object that triggered the event.
    ; ; @param Info: Additional event-specific information (depends on the event type).
    OnEventHandler(GuiCtrlObj, Info) {
        CtrlName := GuiCtrlObj.Name ? GuiCtrlObj.Name : "Control"
        CtrlText := GuiCtrlObj.Text ? GuiCtrlObj.Text : (GuiCtrlObj.Value != "" ? GuiCtrlObj.Value : "")

        if (GuiCtrlObj == ButtonSearch && EditRollSearch.Value != "") {
            ; Use the new universal reset system for search
            UniversalGUIReset("search", true)  ; Reset for search, preserve roll number

            ; Reset verification section
            VerificationGroupBox.Opt("+Disabled")
            VerifyPhotoImage.Value := "img\default_photo.png"
            VerifyFingerprintImage.Value := "img\default_fingerprint.png"
            VerifyRightFingerprintImage.Value := "img\default_fingerprint.png"

            ; Reset webcam feed to default image and set camera to inactive state
            WebcamFeed.Value := "img\default_photo.png"

            ; Set camera to uniform inactive state during search
            SetCameraInactiveState()

            ; Handle signature verification based on configuration
            if (SignatureVerificationEnabled) {
                SignatureVerifyImage.Value := "img\default_signature.png"
                SignatureVerifyImage.Opt("-Disabled")
            } else {
                SignatureVerifyImage.Value := "img\gray.png"
                SignatureVerifyImage.Opt("+Disabled")
            }

            ; Reset verification statuses
            PhotoStatusValue.Text := "-"
            PhotoStatusValue.Opt("cGray")
            ButtonVerifyPicture.Enabled := false
            ButtonVerifyPicture.Text := "Verify Photo"

            FingerprintStatusValue.Text := "-"
            FingerprintStatusValue.Opt("cGray")
            ButtonVerifyFingerprint.Enabled := false
            if (FingerprintMode == "save") {
                ButtonVerifyFingerprint.Text := "Save ThumbPrint (Left)"
            } else {
                ButtonVerifyFingerprint.Text := "Verify ThumbPrint (Left)"
            }

            ; Reset right thumbprint status
            if (!RightThumbprintVerificationEnabled) {
                RightFingerprintStatusValue.Text := "Disabled"
                RightFingerprintStatusValue.Opt("cGray")
            } else {
                RightFingerprintStatusValue.Text := "-"
                RightFingerprintStatusValue.Opt("cGray")
            }
            ButtonVerifyRightFingerprint.Enabled := false
            if (FingerprintMode == "save") {
                ButtonVerifyRightFingerprint.Text := "Save ThumbPrint (Right)"
            } else {
                ButtonVerifyRightFingerprint.Text := "Verify ThumbPrint (Right)"
            }

            ; Handle signature status based on configuration
            if (SignatureVerificationEnabled) {
                ; If signature verification is enabled, set status to dash
                SignatureStatusValue.Text := "-"
                SignatureStatusValue.Opt("cGray")
                ButtonVerifySignature.Enabled := false
                ButtonVerifySignature.Text := "Verify Signature"
                SignatureVerifyImage.Opt("-Disabled")
                SignatureCaptureImage.Opt("-Disabled")
            } else {
                ; If signature verification is disabled, hide/disable all signature-related controls
                SignatureStatusValue.Text := "Disabled"
                SignatureStatusValue.Opt("cGray")
                ButtonVerifySignature.Enabled := false
                ButtonVerifySignature.Text := "Disabled"
                SignatureVerifyImage.Opt("+Disabled")
                SignatureCaptureImage.Opt("+Disabled")

                ; Set the signature images to gray
                SignatureVerifyImage.Value := "img\gray.png"
                SignatureCaptureImage.Value := "img\gray.png"

                OutputDebug("Signature verification disabled - status set to Disabled")
            }

            ; Reset overall verification status
            VerificationStatusValue.Text := "-"
            VerificationStatusValue.Opt("cGray")
            PostVerificationStatusValue.Text := "-"
            PostVerificationStatusValue.Opt("cGray")
            AssignedSeatValue.Text := "-"
            ButtonAssignSeat.Enabled := false

            ; Reset captured paths
            capturedPhotoPath := ""
            capturedFingerprintPath := ""
            capturedSignaturePath := ""

            ; Load candidate data from the database
            rollNumber := EditRollSearch.Value
            candidateData := LoadCandidateData(rollNumber)

            ; Reset all candidate information fields when searching
            ; This ensures a clean state regardless of search result
            EditNameSearch.Value := "Searching..."
            EditNameSearch.Opt("")  ; Reset text color to default
            CandidateNameText.Text := "-"
            FatherNameText.Text := "-"
            GenderText.Text := "-"
            DateOfBirthText.Text := "-"
            LanguageText.Text := "-"
            SpecialStatusText.Text := "-"
            RegisteredPhoto.Value := "img\default_photo.png"
            RegisteredSignature.Value := "img\default_signature.png"

            if (candidateData.Name != "") {
                ; Check if candidate is Active
                if (candidateData.Status != "Active") {
                    EditNameSearch.Value := "Not Active"
                    EditNameSearch.Opt("cRed")  ; Set text color to red

                    ; Disable and gray out all verification status fields in the right panel
                    ; Photo Status
                    PhotoStatusValue.Text := "-"
                    PhotoStatusValue.Opt("cGray")
                    ButtonVerifyPicture.Enabled := false

                    ; ThumbPrint Status
                    FingerprintStatusValue.Text := "-"
                    FingerprintStatusValue.Opt("cGray")
                    ButtonVerifyFingerprint.Enabled := false

                    ; Signature Status
                    SignatureStatusValue.Text := "-"
                    SignatureStatusValue.Opt("cGray")
                    ButtonVerifySignature.Enabled := false

                    ; Verification Status
                    VerificationStatusValue.Text := "-"
                    VerificationStatusValue.Opt("cGray")

                    ; Assigned Seat
                    AssignedSeatValue.Text := "-"
                    AssignedSeatValue.Opt("cGray")
                    ButtonAssignSeat.Enabled := false

                    ; Ensure camera remains disabled for non-active candidates
                    EnableCameraForValidCandidate(candidateData.Status, "")

                    StatusCallback("This candidate is not Active. Only Active candidates can use the system.")
                    return
                }

                ; Update the candidate information section with loaded data
                EditNameSearch.Value := "Candidate Found"
                EditNameSearch.Opt("cGreen")  ; Set text color to green

                ; Update candidate info
                CandidateNameText.Text := candidateData.Name
                FatherNameText.Text := candidateData.FatherName
                GenderText.Text := candidateData.Gender
                ; Format date of birth as DD-MM-YYYY if it's in a standard format
                if (RegExMatch(candidateData.DateOfBirth, "(\d{2})/(\d{2})/(\d{4})", &match)) {
                    DateOfBirthText.Text := match[1] "-" match[2] "-" match[3]
                } else if (RegExMatch(candidateData.DateOfBirth, "(\d{4})-(\d{2})-(\d{2})", &match)) {
                    DateOfBirthText.Text := match[3] "-" match[2] "-" match[1]
                } else if (RegExMatch(candidateData.DateOfBirth, "(\d{2})-(\d{2})-(\d{4})", &match)) {
                    ; Already in DD-MM-YYYY format
                    DateOfBirthText.Text := candidateData.DateOfBirth
                } else {
                    ; Try to parse any other date format and convert to DD-MM-YYYY
                    try {
                        ; Try to parse the date using FormatTime
                        parsedDate := DateParse(candidateData.DateOfBirth)
                        if (parsedDate)
                            DateOfBirthText.Text := FormatTime(parsedDate, "dd-MM-yyyy")
                        else
                            DateOfBirthText.Text := candidateData.DateOfBirth
                    } catch {
                        DateOfBirthText.Text := candidateData.DateOfBirth
                    }
                }
                LanguageText.Text := candidateData.PreferredLanguage

                ; Display special status (normalize to Yes/No for display)
                if (candidateData.Special == "1") {
                    SpecialStatusText.Text := "Yes"
                    SpecialStatusText.Opt("cRed") ; Make it red to highlight special status
                } else {
                    SpecialStatusText.Text := "No"
                    ;SpecialStatusText.Opt("cGreen")
                    SpecialStatusText.Opt("") ; Reset color
                }

                ; Update biometric images if they exist
                if (FileExist(candidateData.Picture))
                    RegisteredPhoto.Value := candidateData.Picture
                else
                    RegisteredPhoto.Value := "img\default_photo.png"

                if (FileExist(candidateData.Signature))
                    RegisteredSignature.Value := candidateData.Signature
                else
                    RegisteredSignature.Value := "img\default_signature.png"

                ; Check if candidate already has a seat assigned for today
                assignedSeat := g_dbManager.GetCandidateSeat(rollNumber)

                ; Check if this is post-exam verification
                global g_isPostExamMode, PostExamModeEnabled, g_invalidPostExam, g_specialDialogShown

                ; Initialize the invalid post-exam flag and special dialog flag
                g_invalidPostExam := false
                g_specialDialogShown := false

                ; First check if post-exam mode is enabled globally
                if (PostExamModeEnabled) {
                    ; In post-exam mode, we need to check if the candidate has a valid seat assignment
                    if (assignedSeat != "") {
                        ; Candidate has a seat, check if they should be in post-exam verification
                        g_isPostExamMode := IsPostExamVerification(rollNumber)
                        g_invalidPostExam := false
                    } else {
                        ; Candidate has no seat assigned but we're in post-exam mode
                        g_isPostExamMode := true
                        g_invalidPostExam := true
                        ErrorHandler.LogMessage("WARNING", "Post-exam verification attempted for candidate without seat assignment: " rollNumber)
                    }
                } else {
                    ; Post-exam mode is not enabled globally
                    g_isPostExamMode := false
                    g_invalidPostExam := false
                }

                ; Handle post-exam mode UI updates
                if (g_isPostExamMode) {
                    ErrorHandler.LogMessage("INFO", "Entering post-exam verification mode for candidate: " rollNumber)

                    ; Check if this is an invalid post-exam verification (no pre-exam seat)
                    if (g_invalidPostExam) {
                        ; Update search status to indicate no pre-exam seat found
                        EditNameSearch.Value := "No pre-exam seat found"
                        EditNameSearch.Opt("cRed")  ; Set text color to red

                        ; Update status bar
                        SafeUpdateStatusBar("POST-EXAM VERIFICATION ERROR - Candidate has no pre-exam seat assignment")

                        ; Update assigned seat value
                        AssignedSeatValue.Text := "Not Assigned"
                        AssignedSeatValue.Opt("cRed")

                        ; Hide the assign seat button in post-exam mode
                        ButtonAssignSeat.Visible := false

                        ; Disable capture and verification sections
                        CaptureGroupBox.Opt("+Disabled")
                        VerificationGroupBox.Opt("+Disabled")

                        ; Set post verification status to error
                        PostVerificationStatusValue.Text := "Error"
                        PostVerificationStatusValue.Opt("cRed")

                        ; Return early to prevent further processing
                        return
                    } else {
                        ; Valid post-exam verification
                        ; Update search status to indicate post-exam verification
                        EditNameSearch.Value := "Post-Exam Verification"
                        EditNameSearch.Opt("cRed")  ; Set text color to red

                        ; Update status bar
                        SafeUpdateStatusBar("POST-EXAM VERIFICATION MODE - Candidate: " rollNumber)

                        ; Update assigned seat value
                        AssignedSeatValue.Text := assignedSeat
                        AssignedSeatValue.Opt("cGreen")

                        ; Hide the assign seat button in post-exam mode
                        ButtonAssignSeat.Visible := false

                        ; Enable capture section
                        CaptureGroupBox.Opt("-Disabled")
                        ButtonCapturePicture.Enabled := true
                        ButtonCaptureFingerprint.Enabled := true

                        ; Enable verification section
                        VerificationGroupBox.Opt("-Disabled")
                    }

                    ; Check if candidate has special status
                    isSpecialCandidate := false
                    try {
                        isSpecialCandidate := (candidateData.Special == "1")
                    } catch {
                        ; If Special field doesn't exist, assume not special
                        isSpecialCandidate := false
                    }

                    if (isSpecialCandidate) {
                        ErrorHandler.LogMessage("INFO", "Special candidate detected in post-exam mode: " rollNumber)

                        ; Show the special case indicator
                        SpecialCaseIndicator.Visible := true

                        ; Get the stored thumb preference if available
                        storedPreference := candidateData.ThumbPreference
                        OutputDebug("Using stored thumb preference for special candidate in post-exam mode: " storedPreference)

                        ; Determine if we're in single thumb mode (no dual thumb support)
                        singleThumbMode := !RightThumbprintVerificationEnabled
                        OutputDebug("Single thumb mode for special candidate in post-exam mode: " (singleThumbMode ? "Yes" : "No"))

                        ; Hide all standard verification controls for special candidates in post-exam mode
                        ; This provides a simplified interface for special accommodation candidates
                        SingleThumbCheckbox.Visible := false
                        SingleThumbCheckbox.Enabled := false
                        LeftThumbRadio.Visible := false
                        LeftThumbRadio.Enabled := false
                        RightThumbRadio.Visible := false
                        RightThumbRadio.Enabled := false
                        EnableRightThumbCheckbox.Visible := false
                        EnableRightThumbCheckbox.Enabled := false

                        ; In post-exam mode, skip the dialog and automatically configure based on stored preference
                        ; This ensures consistency with pre-exam thumb configuration
                        OutputDebug("Post-exam mode: Skipping special accommodation dialog, using stored preference: " storedPreference)

                        ; Configure special accommodation based on stored preference without showing dialog
                        specialAccommodation := {}
                        if (storedPreference == "Left") {
                            specialAccommodation.leftThumb := true
                            specialAccommodation.rightThumb := false
                            OutputDebug("Post-exam mode: Configured for left thumb only based on stored preference")
                        } else if (storedPreference == "Right") {
                            specialAccommodation.leftThumb := false
                            specialAccommodation.rightThumb := true
                            OutputDebug("Post-exam mode: Configured for right thumb only based on stored preference")
                        } else {
                            ; Default to "Both" for any other value
                            specialAccommodation.leftThumb := true
                            specialAccommodation.rightThumb := true
                            OutputDebug("Post-exam mode: Configured for both thumbs based on stored preference")
                        }

                        ; Store the special accommodation selection globally for use during fingerprint capture
                        global g_specialAccommodation
                        g_specialAccommodation := {
                            leftThumb: specialAccommodation.leftThumb,
                            rightThumb: specialAccommodation.rightThumb,
                            isSpecialCandidate: true
                        }
                        OutputDebug("Stored special accommodation selection globally (post-exam): leftThumb=" g_specialAccommodation.leftThumb ", rightThumb=" g_specialAccommodation.rightThumb)

                        ; Configure capture buttons based on stored preference for special candidates in post-exam mode
                        ; This ensures the correct buttons are enabled/disabled without showing any dialogs
                        if (specialAccommodation.leftThumb && specialAccommodation.rightThumb) {
                            ; Both thumbs selected
                            if (RightThumbprintVerificationEnabled) {
                                ; In dual thumb mode, enable both thumb controls
                                ButtonCaptureFingerprint.Enabled := true
                                ButtonCaptureRightFingerprint.Enabled := true

                                ; Reset status values
                                FingerprintStatusValue.Text := "-"
                                FingerprintStatusValue.Opt("cGray")
                                RightFingerprintStatusValue.Text := "-"
                                RightFingerprintStatusValue.Opt("cGray")

                                OutputDebug("Post-exam mode: Special candidate - Both thumbs enabled for capture")
                                SafeUpdateStatusBar("Post-exam verification: Both thumbs will be captured (stored preference)")
                            } else {
                                ; In single thumb mode, we can't capture both, so default to left thumb
                                ButtonCaptureFingerprint.Enabled := true
                                ButtonCaptureRightFingerprint.Enabled := false

                                ; Reset status values
                                FingerprintStatusValue.Text := "-"
                                FingerprintStatusValue.Opt("cGray")
                                RightFingerprintStatusValue.Text := "Disabled"
                                RightFingerprintStatusValue.Opt("cGray")

                                OutputDebug("Post-exam mode: Special candidate - Only left thumb enabled (dual thumb mode not enabled)")
                                SafeUpdateStatusBar("Post-exam verification: Only left thumb will be captured (dual thumb mode not enabled)")
                            }
                        }
                        else if (specialAccommodation.leftThumb) {
                            ; Left thumb only selected
                            ButtonCaptureFingerprint.Enabled := true
                            ButtonCaptureRightFingerprint.Enabled := false

                            ; Reset status values
                            FingerprintStatusValue.Text := "-"
                            FingerprintStatusValue.Opt("cGray")
                            RightFingerprintStatusValue.Text := "Disabled"
                            RightFingerprintStatusValue.Opt("cGray")

                            OutputDebug("Post-exam mode: Special candidate - Only left thumb enabled for capture")
                            SafeUpdateStatusBar("Post-exam verification: Only left thumb will be captured (stored preference)")
                        }
                        else if (specialAccommodation.rightThumb) {
                            ; Right thumb only selected
                            ButtonCaptureFingerprint.Enabled := false
                            ButtonCaptureRightFingerprint.Enabled := true

                            ; Reset status values
                            FingerprintStatusValue.Text := "Disabled"
                            FingerprintStatusValue.Opt("cGray")
                            RightFingerprintStatusValue.Text := "-"
                            RightFingerprintStatusValue.Opt("cGray")

                            OutputDebug("Post-exam mode: Special candidate - Only right thumb enabled for capture")
                            SafeUpdateStatusBar("Post-exam verification: Only right thumb will be captured (stored preference)")
                        }

                        ; Ensure all controls remain hidden for special candidates in post-exam mode
                        SingleThumbCheckbox.Visible := false
                        SingleThumbCheckbox.Enabled := false
                        LeftThumbRadio.Visible := false
                        LeftThumbRadio.Enabled := false
                        RightThumbRadio.Visible := false
                        RightThumbRadio.Enabled := false
                        EnableRightThumbCheckbox.Visible := false
                        EnableRightThumbCheckbox.Enabled := false

                        ; Set internal values based on selection but don't show controls
                        if (!specialAccommodation.leftThumb && specialAccommodation.rightThumb) {
                            ; Right thumb selected - set internal value but keep control hidden
                            EnableRightThumbCheckbox.Value := true
                            SpecialCaseIndicator.Visible := true
                            OutputDebug("Special candidate in post-exam mode: Right thumb selected, controls remain hidden")
                        } else {
                            ; Left thumb selected (default) - set internal value but keep control hidden
                            EnableRightThumbCheckbox.Value := false
                            SpecialCaseIndicator.Visible := false
                            OutputDebug("Special candidate in post-exam mode: Left thumb selected, controls remain hidden")
                        }
                    } else {
                        ; Non-special candidate in post-exam mode - ensure all standard verification controls are available
                        ; This provides full interface access for regular candidates
                        SpecialCaseIndicator.Visible := false

                        ; Reset the special accommodation selection for non-special candidates
                        global g_specialAccommodation
                        g_specialAccommodation := {leftThumb: false, rightThumb: false, isSpecialCandidate: false}
                        OutputDebug("Reset special accommodation selection for non-special candidate (post-exam)")

                        ; Configure controls based on stored ThumbPreference for non-special candidates in post-exam mode
                        ; Post-exam mode must use the exact same thumb configuration as pre-exam mode
                        storedPreference := candidateData.ThumbPreference
                        OutputDebug("Using stored thumb preference for non-special candidate in post-exam mode: " storedPreference)

                        ; Disable all thumb selection controls - no changes allowed in post-exam mode
                        SingleThumbCheckbox.Visible := false
                        SingleThumbCheckbox.Enabled := false
                        LeftThumbRadio.Visible := false
                        LeftThumbRadio.Enabled := false
                        RightThumbRadio.Visible := false
                        RightThumbRadio.Enabled := false
                        EnableRightThumbCheckbox.Visible := false
                        EnableRightThumbCheckbox.Enabled := false

                        ; Set internal control values based on stored preference without showing controls
                        if (RightThumbprintVerificationEnabled) {
                            ; DualThumb mode: Configure based on stored preference
                            if (storedPreference == "Left") {
                                ; Single left thumb was used in pre-exam
                                SingleThumbCheckbox.Value := true  ; Single thumb mode
                                LeftThumbRadio.Value := true       ; Left thumb selected
                                RightThumbRadio.Value := false
                                EnableRightThumbCheckbox.Value := false
                                OutputDebug("Post-exam mode: Configured for single left thumb (stored preference: Left)")
                            } else if (storedPreference == "Right") {
                                ; Single right thumb was used in pre-exam
                                SingleThumbCheckbox.Value := true  ; Single thumb mode
                                LeftThumbRadio.Value := false
                                RightThumbRadio.Value := true      ; Right thumb selected
                                EnableRightThumbCheckbox.Value := false
                                OutputDebug("Post-exam mode: Configured for single right thumb (stored preference: Right)")
                            } else {
                                ; Both thumbs were used in pre-exam (default "Both")
                                SingleThumbCheckbox.Value := false ; Both thumbs mode
                                LeftThumbRadio.Value := false
                                RightThumbRadio.Value := false
                                EnableRightThumbCheckbox.Value := false
                                OutputDebug("Post-exam mode: Configured for both thumbs (stored preference: Both)")
                            }
                        } else {
                            ; SingleThumb mode: Configure based on stored preference
                            if (storedPreference == "Right") {
                                ; Right thumb was used in pre-exam
                                EnableRightThumbCheckbox.Value := true
                                OutputDebug("Post-exam mode: Configured for right thumb (stored preference: Right)")
                            } else {
                                ; Left thumb was used in pre-exam (default for "Left" or "Both")
                                EnableRightThumbCheckbox.Value := false
                                OutputDebug("Post-exam mode: Configured for left thumb (stored preference: " storedPreference ")")
                            }
                            SingleThumbCheckbox.Value := false
                            LeftThumbRadio.Value := false
                            RightThumbRadio.Value := false
                        }

                        OutputDebug("Non-special candidate in post-exam mode: Thumb selection controls disabled, using stored preference: " storedPreference)
                    }

                    ; Reset all biometric status fields for post-exam capture workflow
                    ; This allows fresh capture and verification for post-exam mode
                    PhotoStatusValue.Text := "-"
                    PhotoStatusValue.Opt("cGray")
                    ButtonVerifyPicture.Enabled := false
                    ErrorHandler.LogMessage("INFO", "Reset photo status for post-exam capture")

                    FingerprintStatusValue.Text := "-"
                    FingerprintStatusValue.Opt("cGray")
                    ButtonVerifyFingerprint.Enabled := false
                    ErrorHandler.LogMessage("INFO", "Reset left fingerprint status for post-exam capture")

                    if (RightThumbprintVerificationEnabled) {
                        RightFingerprintStatusValue.Text := "-"
                        RightFingerprintStatusValue.Opt("cGray")
                        ButtonVerifyRightFingerprint.Enabled := false
                        ErrorHandler.LogMessage("INFO", "Reset right fingerprint status for post-exam capture")
                    } else {
                        RightFingerprintStatusValue.Text := "Disabled"
                        RightFingerprintStatusValue.Opt("cGray")
                        ButtonVerifyRightFingerprint.Enabled := false
                    }

                    if (SignatureVerificationEnabled) {
                        SignatureStatusValue.Text := "-"
                        SignatureStatusValue.Opt("cGray")
                        ButtonVerifySignature.Enabled := false
                        ErrorHandler.LogMessage("INFO", "Reset signature status for post-exam capture")
                    } else {
                        SignatureStatusValue.Text := "Disabled"
                        SignatureStatusValue.Opt("cGray")
                        ButtonVerifySignature.Enabled := false
                    }

                    ; Define candidates database path for post-exam status updates
                    global dbPath
                    if (SubStr(dbPath, 1, 1) != "\" && SubStr(dbPath, 2, 1) != ":") {
                        dbPathFull := A_ScriptDir "\" dbPath
                    } else {
                        dbPathFull := dbPath
                    }
                    candidatesPath := dbPathFull "\candidates.ini"

                    ; Check overall verification status and display post-exam specific status
                    UpdatePostExamStatusDisplay(rollNumber, candidateData, candidatesPath)

                    ; Update pre-verification status
                    if (candidateData.BiometricStatus == "Verified") {
                        VerificationStatusValue.Text := "Completed"
                        VerificationStatusValue.Opt("cGreen")
                    } else {
                        VerificationStatusValue.Text := "Incomplete"
                        VerificationStatusValue.Opt("cRed")
                    }

                    ; Continue with normal flow - don't return here
                } else if (assignedSeat != "") {
                    ; Regular mode with seat already assigned

                    ; Update search status to indicate seat is already assigned
                    EditNameSearch.Value := "Seat already assigned"
                    EditNameSearch.Opt("cBlue")

                    ; Update verification status to show verified
                    VerificationStatusValue.Text := "Verified"
                    VerificationStatusValue.Opt("cGreen")

                    ; Update assigned seat value but don't show details
                    AssignedSeatValue.Text := "Assigned"
                    AssignedSeatValue.Opt("cGreen")

                    ; Ensure seat details text is hidden for security
                    if (IsObject(SeatDetailsText)) {
                        SeatDetailsText.Visible := false
                        SeatDetailsText.Text := ""  ; Clear the text as well
                        OutputDebug("Seat details hidden for candidate with already assigned seat")
                    }

                    ; Set camera to inactive state for candidates with already assigned seats
                    SetCameraInactiveState()

                    ; Hide the assign seat button since seat is already assigned
                    ButtonAssignSeat.Visible := false

                    SafeUpdateStatusBar("Candidate already has a seat assigned")
                    return
                } else {
                    ; Regular mode with no seat assigned

                    AssignedSeatValue.Text := "---"
                    ButtonAssignSeat.Visible := true
                }

                ; Check biometric status - skip this section if in post-exam mode
                ; In post-exam mode, we want fresh verification workflow regardless of pre-exam status
                if (!g_isPostExamMode && candidateData.BiometricStatus == "Verified") {
                    ; Enable verification panel to show status
                    VerificationGroupBox.Opt("-Disabled")

                    ; Set verification statuses based on database values
                    if (candidateData.PhotoStatus == "Verified") {
                        PhotoStatusValue.Text := "Verified"
                        PhotoStatusValue.Opt("cGreen")
                        ButtonVerifyPicture.Enabled := false
                    }

                    if (candidateData.FingerprintStatus == "Verified") {
                        FingerprintStatusValue.Text := "Verified"
                        FingerprintStatusValue.Opt("cGreen")
                        ButtonVerifyFingerprint.Enabled := false
                    }

                    ; Check right fingerprint status
                    if (candidateData.RightFingerprintStatus == "Verified") {
                        RightFingerprintStatusValue.Text := "Verified"
                        RightFingerprintStatusValue.Opt("cGreen")
                        ButtonVerifyRightFingerprint.Enabled := false
                    } else if (!RightThumbprintVerificationEnabled && !EnableRightThumbCheckbox.Value) {
                        ; Only show as disabled if global setting is off AND checkbox is not checked
                        RightFingerprintStatusValue.Text := "Disabled"
                        RightFingerprintStatusValue.Opt("cGray")
                        ButtonVerifyRightFingerprint.Enabled := false
                    }

                    ; Update button states based on loaded verification status
                    UpdateButtonStatesBasedOnVerificationStatus()

                    if (!SignatureVerificationEnabled) {
                        SignatureStatusValue.Text := "Disabled"
                        SignatureStatusValue.Opt("cGray")
                    } else if (candidateData.SignatureStatus == "Verified") {
                        SignatureStatusValue.Text := "Verified"
                        SignatureStatusValue.Opt("cGreen")
                        ButtonVerifySignature.Enabled := false
                    }

                    ; Set overall verification status to completed
                    VerificationStatusValue.Text := "Completed"
                    VerificationStatusValue.Opt("cGreen")

                    ; Enable seat assignment button
                    ButtonAssignSeat.Enabled := true

                    ; Make sure the capture photo button is visible and review buttons are hidden
                    ButtonCapturePicture.Visible := true
                    ReviewPhotoLabel.Visible := false
                    ButtonUsePhoto.Visible := false
                    ButtonRecapturePhoto.Visible := false

                    ; Set camera to inactive state for candidates with completed verification
                    SetCameraInactiveState()

                    SafeUpdateStatusBar("Biometric verification already completed for this candidate. Ready for seat assignment.")
                    return
                }

                ; Enable the Capture section now that we have a valid candidate
                CaptureGroupBox.Opt("-Disabled")

                ; Activate camera for valid candidate and enable photo capture button
                SetCameraActiveState(candidateData.Status)

                ; Enable photo capture button explicitly for valid candidates
                if (IsSet(ButtonCapturePicture) && IsObject(ButtonCapturePicture)) {
                    ButtonCapturePicture.Enabled := true
                    OutputDebug("Candidate search: Photo capture button enabled for valid candidate")
                }

                ; Enable fingerprint capture buttons
                ButtonCaptureFingerprint.Enabled := true

                ; Enable the right thumb checkbox as a manual override option
                ; for candidates who can only use their right thumb
                ; Visibility will be set based on RightThumbprintVerification setting
                EnableRightThumbCheckbox.Enabled := true

                ; Configure UI based on RightThumbprintVerification setting
                if (RightThumbprintVerificationEnabled) {
                    ; In dual thumb mode, show the single thumb checkbox
                    SingleThumbCheckbox.Visible := true
                    SingleThumbCheckbox.Enabled := true
                    SingleThumbCheckbox.Value := false

                    ; Hide the "Use Right Thumb Instead" checkbox in dual thumb mode
                    EnableRightThumbCheckbox.Visible := false

                    ; Hide the radio buttons initially
                    LeftThumbRadio.Visible := false
                    RightThumbRadio.Visible := false

                    ; Enable right thumbprint button
                    ButtonCaptureRightFingerprint.Enabled := true
                } else {
                    ; In single thumb mode, hide the single thumb checkbox and radio buttons
                    SingleThumbCheckbox.Visible := false
                    LeftThumbRadio.Visible := false
                    RightThumbRadio.Visible := false

                    ; Show the "Use Right Thumb Instead" checkbox in single thumb mode
                    EnableRightThumbCheckbox.Visible := true
                    EnableRightThumbCheckbox.Enabled := true

                    ; When global setting is disabled, the right thumbprint button is initially disabled
                    ; but can be enabled via the checkbox
                    ButtonCaptureRightFingerprint.Enabled := false
                }

                ; Hide the special case indicator initially
                SpecialCaseIndicator.Visible := false

                ; Check if the candidate has a special status flag
                if (candidateData.Special == "1") {
                    ; Show the special case indicator
                    SpecialCaseIndicator.Visible := true

                    ; Get the stored thumb preference if available
                    storedPreference := candidateData.ThumbPreference
                    OutputDebug("Using stored thumb preference for special candidate: " storedPreference)

                    ; Determine if we're in single thumb mode (no dual thumb support)
                    singleThumbMode := !RightThumbprintVerificationEnabled
                    OutputDebug("Single thumb mode for special dialog: " (singleThumbMode ? "Yes" : "No"))

                    ; Hide all standard verification controls for special candidates
                    ; This provides a simplified interface for special accommodation candidates
                    SingleThumbCheckbox.Visible := false
                    SingleThumbCheckbox.Enabled := false
                    LeftThumbRadio.Visible := false
                    LeftThumbRadio.Enabled := false
                    RightThumbRadio.Visible := false
                    RightThumbRadio.Enabled := false
                    EnableRightThumbCheckbox.Visible := false
                    EnableRightThumbCheckbox.Enabled := false

                    ; Handle special accommodation dialog based on mode
                    if (g_isPostExamMode) {
                        ; In post-exam mode, completely skip the dialog and use stored preference
                        ; This ensures consistency with pre-exam thumb configuration
                        OutputDebug("Post-exam mode: Skipping special accommodation dialog, using stored preference: " storedPreference)

                        ; Configure special accommodation based on stored preference without showing dialog
                        specialAccommodation := {}
                        if (storedPreference == "Left") {
                            specialAccommodation.leftThumb := true
                            specialAccommodation.rightThumb := false
                            OutputDebug("Post-exam mode: Configured for left thumb only based on stored preference")
                        } else if (storedPreference == "Right") {
                            specialAccommodation.leftThumb := false
                            specialAccommodation.rightThumb := true
                            OutputDebug("Post-exam mode: Configured for right thumb only based on stored preference")
                        } else {
                            ; Default to "Both" for any other value
                            specialAccommodation.leftThumb := true
                            specialAccommodation.rightThumb := true
                            OutputDebug("Post-exam mode: Configured for both thumbs based on stored preference")
                        }
                    } else if (!g_specialDialogShown) {
                        ; Pre-exam mode: Show the special accommodation dialog with the stored preference and mode
                        specialAccommodation := ShowSpecialAccommodationDialog(rollNumber, storedPreference, singleThumbMode)
                        g_specialDialogShown := true  ; Mark that dialog has been shown
                        OutputDebug("Pre-exam mode: Showed special accommodation dialog for candidate " rollNumber)
                    } else {
                        ; Dialog already shown in pre-exam mode, use existing selection
                        specialAccommodation := g_specialAccommodation
                        OutputDebug("Pre-exam mode: Using existing special accommodation selection (dialog already shown)")
                    }

                    ; Store the special accommodation selection globally for use during fingerprint capture
                    global g_specialAccommodation
                    g_specialAccommodation := {
                        leftThumb: specialAccommodation.leftThumb,
                        rightThumb: specialAccommodation.rightThumb,
                        isSpecialCandidate: true
                    }
                    OutputDebug("Stored special accommodation selection globally: leftThumb=" g_specialAccommodation.leftThumb ", rightThumb=" g_specialAccommodation.rightThumb)

                    ; For special candidates, keep all controls hidden regardless of selection
                    ; The special accommodation dialog handles the thumb selection internally
                    ; No additional UI controls are needed below the capture buttons

                    ; Ensure all controls remain hidden for special candidates
                    SingleThumbCheckbox.Visible := false
                    SingleThumbCheckbox.Enabled := false
                    LeftThumbRadio.Visible := false
                    LeftThumbRadio.Enabled := false
                    RightThumbRadio.Visible := false
                    RightThumbRadio.Enabled := false
                    EnableRightThumbCheckbox.Visible := false
                    EnableRightThumbCheckbox.Enabled := false

                    ; Set internal values based on selection but don't show controls
                    if (!specialAccommodation.leftThumb && specialAccommodation.rightThumb) {
                        ; Right thumb selected - set internal value but keep control hidden
                        EnableRightThumbCheckbox.Value := true
                        SpecialCaseIndicator.Visible := true
                        OutputDebug("Special candidate: Right thumb selected, controls remain hidden")
                    } else {
                        ; Left thumb selected (default) - set internal value but keep control hidden
                        EnableRightThumbCheckbox.Value := false
                        SpecialCaseIndicator.Visible := false
                        OutputDebug("Special candidate: Left thumb selected, controls remain hidden")
                    }

                    if (specialAccommodation.leftThumb && specialAccommodation.rightThumb) {
                        ; Both thumbs selected
                        if (RightThumbprintVerificationEnabled) {
                            ; In dual thumb mode, enable both thumb controls
                            ButtonCaptureFingerprint.Enabled := true
                            ButtonCaptureRightFingerprint.Enabled := true

                            ; Reset status values
                            FingerprintStatusValue.Text := "-"
                            FingerprintStatusValue.Opt("cGray")
                            RightFingerprintStatusValue.Text := "-"
                            RightFingerprintStatusValue.Opt("cGray")

                            SafeUpdateStatusBar("Special candidate thumbprint selection: Both thumbs will be captured")
                        } else {
                            ; In single thumb mode, we can't capture both, so default to left thumb
                            ButtonCaptureFingerprint.Enabled := true
                            ButtonCaptureRightFingerprint.Enabled := false

                            ; Reset status values
                            FingerprintStatusValue.Text := "-"
                            FingerprintStatusValue.Opt("cGray")
                            RightFingerprintStatusValue.Text := "Disabled"
                            RightFingerprintStatusValue.Opt("cGray")

                            SafeUpdateStatusBar("Special candidate thumbprint selection: Only left thumb will be captured (dual thumb mode not enabled)")
                        }
                    }
                    else if (specialAccommodation.leftThumb) {
                        ; Left thumb only selected
                        ButtonCaptureFingerprint.Enabled := true
                        ButtonCaptureRightFingerprint.Enabled := false

                        ; Reset status values
                        FingerprintStatusValue.Text := "-"
                        FingerprintStatusValue.Opt("cGray")
                        RightFingerprintStatusValue.Text := "Disabled"
                        RightFingerprintStatusValue.Opt("cGray")

                        SafeUpdateStatusBar("Special candidate thumbprint selection: Only left thumb will be captured")
                    }
                    else if (specialAccommodation.rightThumb) {
                        ; Right thumb only selected
                        ButtonCaptureFingerprint.Enabled := false
                        ButtonCaptureRightFingerprint.Enabled := true

                        ; Reset status values
                        FingerprintStatusValue.Text := "Disabled"
                        FingerprintStatusValue.Opt("cGray")
                        RightFingerprintStatusValue.Text := "-"
                        RightFingerprintStatusValue.Opt("cGray")

                        SafeUpdateStatusBar("Special candidate thumbprint selection: Only right thumb will be captured")
                    }
                } else {
                    ; Non-special candidate - ensure standard verification controls follow fingerprint mode
                    ; This provides appropriate interface access based on configured fingerprint mode
                    SpecialCaseIndicator.Visible := false

                    ; Reset the special accommodation selection for non-special candidates
                    global g_specialAccommodation
                    g_specialAccommodation := {leftThumb: false, rightThumb: false, isSpecialCandidate: false}
                    OutputDebug("Reset special accommodation selection for non-special candidate")

                    ; Configure controls based on fingerprint mode and load stored ThumbPreference
                    storedPreference := candidateData.ThumbPreference
                    OutputDebug("Loading stored thumb preference for non-special candidate: " storedPreference)

                    if (RightThumbprintVerificationEnabled) {
                        ; DualThumb mode: Configure based on stored preference
                        SingleThumbCheckbox.Enabled := true

                        ; Set values based on stored preference
                        if (storedPreference == "Left") {
                            ; Single left thumb preference
                            SingleThumbCheckbox.Value := true
                            LeftThumbRadio.Value := true
                            RightThumbRadio.Value := false
                            LeftThumbRadio.Visible := true
                            LeftThumbRadio.Enabled := true
                            RightThumbRadio.Visible := true
                            RightThumbRadio.Enabled := true
                            OutputDebug("Pre-exam mode: Restored single left thumb preference")
                        } else if (storedPreference == "Right") {
                            ; Single right thumb preference
                            SingleThumbCheckbox.Value := true
                            LeftThumbRadio.Value := false
                            RightThumbRadio.Value := true
                            LeftThumbRadio.Visible := true
                            LeftThumbRadio.Enabled := true
                            RightThumbRadio.Visible := true
                            RightThumbRadio.Enabled := true
                            OutputDebug("Pre-exam mode: Restored single right thumb preference")
                        } else {
                            ; Both thumbs preference (default)
                            SingleThumbCheckbox.Value := false
                            LeftThumbRadio.Visible := false
                            LeftThumbRadio.Enabled := false
                            RightThumbRadio.Visible := false
                            RightThumbRadio.Enabled := false
                            OutputDebug("Pre-exam mode: Restored both thumbs preference")
                        }

                        ; Ensure "Use Right Thumb" checkbox remains hidden in dual thumb mode
                        EnableRightThumbCheckbox.Enabled := false
                        EnableRightThumbCheckbox.Value := false

                        OutputDebug("Non-special candidate: DualThumb mode - Controls configured based on stored preference")
                    } else {
                        ; SingleThumb mode: Configure based on stored preference
                        EnableRightThumbCheckbox.Enabled := true

                        ; Set value based on stored preference
                        if (storedPreference == "Right") {
                            EnableRightThumbCheckbox.Value := true
                            OutputDebug("Pre-exam mode: Restored right thumb preference")
                        } else {
                            EnableRightThumbCheckbox.Value := false
                            OutputDebug("Pre-exam mode: Restored left thumb preference")
                        }

                        ; Ensure other controls remain hidden in single thumb mode
                        SingleThumbCheckbox.Enabled := false
                        SingleThumbCheckbox.Value := false
                        LeftThumbRadio.Visible := false
                        LeftThumbRadio.Enabled := false
                        RightThumbRadio.Visible := false
                        RightThumbRadio.Enabled := false

                        OutputDebug("Non-special candidate: SingleThumb mode - Controls configured based on stored preference")
                    }

                    OutputDebug("Non-special candidate: Standard verification controls configured for fingerprint mode with stored preference: " storedPreference)
                }

                ; Handle signature capture based on configuration
                if (SignatureVerificationEnabled) {
                    ; Enable signature capture if it's configured
                    ButtonCaptureSignature.Enabled := true
                    SignatureCaptureImage.Opt("-Disabled")
                    SignatureCaptureImage.Value := "img\default_signature.png"
                } else {
                    ; Disable signature capture if it's not configured
                    ButtonCaptureSignature.Enabled := false
                    SignatureCaptureImage.Opt("+Disabled")
                    SignatureCaptureImage.Value := "img\gray.png"

                    ; Also check if we need to update verification status
                    ; since we're skipping signature verification
                    CheckAllVerifications()
                }

                StatusCallback("Candidate data loaded successfully: " . candidateData.Name . " | Status: Active | Ready for biometric capture")
            } else {
                ; Display error message in the EditNameSearch field instead of status bar
                EditNameSearch.Value := "Candidate Not Found"
                EditNameSearch.Opt("cRed")  ; Set text color to red

                ; Disable and gray out all verification status fields in the right panel
                ; Photo Status
                PhotoStatusValue.Text := "-"
                PhotoStatusValue.Opt("cGray")
                ButtonVerifyPicture.Enabled := false

                ; ThumbPrint Status
                FingerprintStatusValue.Text := "-"
                FingerprintStatusValue.Opt("cGray")
                ButtonVerifyFingerprint.Enabled := false

                ; Signature Status
                SignatureStatusValue.Text := "-"
                SignatureStatusValue.Opt("cGray")
                ButtonVerifySignature.Enabled := false

                ; Verification Status
                VerificationStatusValue.Text := "-"
                VerificationStatusValue.Opt("cGray")

                ; Post Verification Status
                PostVerificationStatusValue.Text := "-"
                PostVerificationStatusValue.Opt("cGray")

                ; Assigned Seat
                AssignedSeatValue.Text := "-"
                AssignedSeatValue.Opt("cGray")
                ButtonAssignSeat.Enabled := false

                ; Also show in status bar
                StatusCallback("No data found for Roll Number: " rollNumber)
            }
        }

        ; Photo capture handler
        else if (GuiCtrlObj == ButtonCapturePicture) {
            ; Update status bar
            StatusCallback("Capturing photo from camera...")
            OutputDebug("ButtonCapturePicture clicked")

            try {
                ; Create temp directory if it doesn't exist
                tempDir := A_Temp "\wincbt_biometric"
                if (!DirExist(tempDir)) {
                    DirCreate(tempDir)
                    OutputDebug("Created temp directory: " tempDir)
                }

                ; Generate a unique filename using timestamp
                timestamp := FormatTime(, "yyyyMMdd_HHmmss")
                filename := tempDir "\captured_" timestamp ".jpg"
                OutputDebug("Attempting to capture photo to: " filename)

                ; Capture image using webcam_utils.ahk function
                global capHwnd, webcamControl, capturedImageControl

                if (capHwnd && isWebcamActive) {
                    ; Capture the image
                    result := CaptureWebcamImage(webcamControl, capHwnd, filename, "jpg")

                    if (result.success) {
                        ; Store the captured photo path
                        tempCapturedPhotoPath := result.filename
                        OutputDebug("Successfully captured photo at: " tempCapturedPhotoPath)

                        ; Display the captured image
                        capturedImageControl.Value := tempCapturedPhotoPath

                        ; Toggle visibility to show captured image instead of webcam feed
                        ToggleWebcamAndCapturedImage(webcamControl, capturedImageControl, false)

                        ; Hide capture button and show review buttons
                        ButtonCapturePicture.Visible := false
                        ReviewPhotoLabel.Visible := true
                        ButtonUsePhoto.Visible := true
                        ButtonRecapturePhoto.Visible := true

                        ; Stop the webcam to free resources
                        StopWebcam(capHwnd)
                        capHwnd := 0
                        isWebcamActive := false

                        ; Change button text for when webcam is restarted
                        ButtonCapturePicture.Text := "Restart Webcam"

                        StatusCallback("Photo captured - Review and select 'Use This Photo' if the photo is clear")
                    } else {
                        OutputDebug("CaptureWebcamImage failed: " result.error)
                        StatusCallback("Failed to capture image: " result.error)
                    }
                } else {
                    OutputDebug("Error: Webcam not active")
                    StatusCallback("Webcam not active. Cannot capture photo.")
                }
            } catch as err {
                OutputDebug("Error capturing photo: " err.Message)
                StatusCallback("Error capturing photo: " err.Message)
            }
        }

        ; Use Photo handler
        else if (GuiCtrlObj == ButtonUsePhoto) {
            OutputDebug("ButtonUsePhoto clicked")
            OutputDebug("WebcamFeed.Value before check: " WebcamFeed.Value)
            OutputDebug("tempCapturedPhotoPath value: " tempCapturedPhotoPath)

            if (FileExist(tempCapturedPhotoPath)) {
                ; Store the captured photo path
                OutputDebug("File exists at tempCapturedPhotoPath: " tempCapturedPhotoPath)

                try {
                    ; Update verification panel with the captured photo
                    OutputDebug("Setting VerifyPhotoImage.Value to: " tempCapturedPhotoPath)
                    VerifyPhotoImage.Value := tempCapturedPhotoPath
                    VerifyPhotoImage.Redraw()
                    capturedPhotoPath := tempCapturedPhotoPath

                    ; Save the captured photo to the candidate's folder
                    try {
                        ; Get database file paths using PathManager
                        rollNumber := EditRollSearch.Value
                        candidatesImgPath := PathManager.GetDatabaseSubPath("CandidateImages", true)  ; true to validate/create dir if needed

                        ; Determine exam phase
                        global g_isPostExamMode
                        examPhase := g_isPostExamMode ? "post" : "pre"

                        ; Save with the captured photo naming convention only
                        capturedSavePath := candidatesImgPath rollNumber "_captured_photo_" examPhase ".jpg"
                        FileCopy(capturedPhotoPath, capturedSavePath, true)
                        OutputDebug("Saved captured photo to: " capturedSavePath)
                    } catch Error as e {
                        OutputDebug("Error saving captured photo: " e.Message)
                        sbMain.SetText("Warning: Could not save captured photo to database")
                    }

                    ; Hide review buttons and show capture button
                    ReviewPhotoLabel.Visible := false
                    ButtonUsePhoto.Visible := false
                    ButtonRecapturePhoto.Visible := false
                    ButtonCapturePicture.Visible := true
                    ButtonCapturePicture.Text := "Recapture Photo"

                    ; Restart the webcam
                    global capHwnd, webcamControl, capturedImageControl, cameraName, isWebcamActive

                    ; Start the webcam
                    capHwnd := StartWebcam(webcamControl, cameraName)

                    if (capHwnd) {
                        isWebcamActive := true

                        ; Show webcam feed instead of captured image
                        ToggleWebcamAndCapturedImage(webcamControl, capturedImageControl, true)

                        OutputDebug("Webcam restarted successfully")
                    } else {
                        OutputDebug("Failed to restart webcam")
                    }

                    ; Enable verification panel and update status
                    VerificationGroupBox.Opt("-Disabled")

                    ; Auto-verify if configured to do so
                    if (PhotoVerificationMode == "Auto" || PhotoVerificationMode == "Both") {
                        ; Get registered photo path
                        rollNumber := EditRollSearch.Value
                        registeredPhotoPath := PathManager.GetCandidatePhotoPath(rollNumber)

                        ; Verify photo automatically
                        verifyResult := VerifyPhoto(registeredPhotoPath, capturedPhotoPath)

                        ; Ensure confidence is an integer
                        if (verifyResult.HasOwnProp("confidence"))
                            verifyResult.confidence := Integer(verifyResult.confidence)
                        else
                            verifyResult.confidence := 0

                        ; Format confidence as string for display
                        confidenceStr := String(verifyResult.confidence)

                        OutputDebug("Auto photo verification result: " verifyResult.result " with confidence " confidenceStr "%")

                        ; Debug the verifyResult object to ensure confidence is properly set
                        OutputDebug("verifyResult object: result=" verifyResult.result
                                   ", confidence=" confidenceStr
                                   ", message=" verifyResult.message)

                        ; Log the threshold for comparison
                        OutputDebug("PhotoConfidenceThreshold: " PhotoConfidenceThreshold)

                        if (verifyResult.result && verifyResult.confidence >= PhotoConfidenceThreshold) {
                            ; Auto-verification successful
                            PhotoStatusValue.Text := "Verified"
                            PhotoStatusValue.Opt("cGreen")
                            ButtonVerifyPicture.Enabled := false

                            ; Disable recapture button when photo verification is complete
                            if (IsSet(ButtonRecapturePhoto) && IsObject(ButtonRecapturePhoto)) {
                                ButtonRecapturePhoto.Enabled := false
                                OutputDebug("Photo verification complete: Disabled recapture button")
                            }

                            ; Format confidence value consistently
                            confidenceInt := Integer(verifyResult.confidence)
                            confidenceStr := String(confidenceInt)
                            sbMain.SetText("Photo auto-verified with " confidenceStr "% confidence")

                            ; Update photo status in database
                            try {
                                ; Convert relative to absolute path if needed
                                if (SubStr(dbPath, 1, 1) != "\" && SubStr(dbPath, 2, 1) != ":") {
                                    dbPathFull := A_ScriptDir "\" dbPath
                                } else {
                                    dbPathFull := dbPath
                                }

                                ; Define database file path
                                candidatesPath := dbPathFull "\candidates.ini"

                                IniWrite("Verified", candidatesPath, rollNumber, "PhotoStatus")
                                OutputDebug("Updated photo status in database to Verified")
                            } catch Error as e {
                                OutputDebug("Error updating photo status in database: " e.Message)
                            }

                            ; Update button states based on new verification status
                            UpdateButtonStatesBasedOnVerificationStatus()

                            ; Check if all verifications are complete
                            CheckAllVerifications()
                            return
                        } else {
                            ; Auto-verification failed or confidence too low
                            PhotoStatusValue.Text := "Ready"
                            PhotoStatusValue.Opt("cBlue")
                            ButtonVerifyPicture.Enabled := true

                            ; We already have confidenceStr from earlier code
                            ; Just use it directly for consistency

                            ; Log the reason for manual verification
                            if (verifyResult.result) {
                                OutputDebug("Auto-verification result is true but confidence " confidenceStr "% is below threshold " PhotoConfidenceThreshold "%")
                                reasonText := "confidence below threshold"
                            } else {
                                OutputDebug("Auto-verification result is false with confidence " confidenceStr "%")
                                reasonText := verifyResult.message
                            }

                            ; Set the status text with the formatted confidence value
                            statusText := "Photo needs manual verification (confidence: " confidenceStr "%, " reasonText ")"
                            sbMain.SetText(statusText)
                            OutputDebug("Setting status bar with text: " statusText)

                            ; Always verify photo automatically without requiring manual verification
                            ; This is the key change - we'll automatically verify the photo even if confidence is low
                            ;PhotoStatusValue.Text := "Verified"
                            ;PhotoStatusValue.Opt("cGreen")
                            ;ButtonVerifyPicture.Enabled := false
                            ;sbMain.SetText("Photo auto-verified with " confidenceStr "% confidence (below threshold but auto-approved)")

                            ; Update photo status in database
                            try {
                                ; Convert relative to absolute path if needed
                                if (SubStr(dbPath, 1, 1) != "\" && SubStr(dbPath, 2, 1) != ":") {
                                    dbPathFull := A_ScriptDir "\" dbPath
                                } else {
                                    dbPathFull := dbPath
                                }

                                ; Define database file path
                                candidatesPath := dbPathFull "\candidates.ini"

                                IniWrite("Verified", candidatesPath, rollNumber, "PhotoStatus")
                                OutputDebug("Updated photo status in database to Verified (auto-approved despite low confidence)")
                            } catch Error as e {
                                OutputDebug("Error updating photo status in database: " e.Message)
                            }

                            ; Check if all verifications are complete
                            CheckAllVerifications()
                        }
                    } else {
                        ; Manual mode
                        PhotoStatusValue.Text := "Ready"
                        PhotoStatusValue.Opt("cBlue")
                        ButtonVerifyPicture.Enabled := true
                        ButtonVerifyPicture.Text := "Manual Verify"
                        sbMain.SetText("Photo approved - Ready for manual verification")
                    }

                    CheckAllVerifications()
                } catch as err {
                    OutputDebug("Error in Use Photo handler: " err.Message)
                    sbMain.SetText("Error transferring photo to verification panel: " err.Message)
                }
            } else {
                OutputDebug("File does not exist at tempCapturedPhotoPath: " tempCapturedPhotoPath)
                sbMain.SetText("Error: Captured photo file not found")
            }
        }

        ; Recapture Photo handler
        else if (GuiCtrlObj == ButtonRecapturePhoto) {
            OutputDebug("ButtonRecapturePhoto clicked")

            try {
                ; Hide review buttons and show capture button
                ReviewPhotoLabel.Visible := false
                ButtonUsePhoto.Visible := false
                ButtonRecapturePhoto.Visible := false
                ButtonCapturePicture.Visible := true
                ButtonCapturePicture.Text := "Capture Photo"

                ; Reset photo status
                PhotoStatusValue.Text := "Pending"
                PhotoStatusValue.Opt("cRed")
                ButtonVerifyPicture.Enabled := false

                ; Clear verification panel photo
                VerifyPhotoImage.Value := "img\default_photo.png"

                ; Restart the webcam
                global capHwnd, webcamControl, capturedImageControl, cameraName, isWebcamActive

                ; Start the webcam
                capHwnd := StartWebcam(webcamControl, cameraName)

                if (capHwnd) {
                    isWebcamActive := true

                    ; Show webcam feed instead of captured image
                    ToggleWebcamAndCapturedImage(webcamControl, capturedImageControl, true)

                    OutputDebug("Webcam restarted successfully for recapture")
                    sbMain.SetText("Ready to capture new photo")
                } else {
                    OutputDebug("Failed to restart webcam for recapture")
                    sbMain.SetText("Error: Failed to restart webcam")
                }
            } catch as err {
                OutputDebug("Error in recapture handler: " err.Message)
                sbMain.SetText("Error resetting camera: " err.Message)
            }
        }

        else if (GuiCtrlObj == ButtonCaptureFingerprint) {
            ; Start fingerprint capture state management
            if (!StartFingerprintCapture(true)) {
                return  ; Another capture is in progress
            }

            ; Check if fingerprint manager is available
            if (IsObject(g_fingerprintManager)) {
                try {
                    ; Initialize the device if not already initialized
                    if (!SecuGenFingerprint.sgfplib) {
                        try {
                            g_fingerprintManager.Init()
                            OutputDebug("Fingerprint device initialized")
                        } catch Error as e {
                            OutputDebug("Error initializing fingerprint device: " e.Message)
                            try {
                                sbMain.Text := "Error initializing fingerprint device: " e.Message
                            } catch as err {
                                OutputDebug("Error updating status bar: " err.Message)
                            }
                            ; Complete fingerprint capture with error to restore button state
                            CompleteFingerprintCapture(true, false, "Error initializing fingerprint device: " e.Message)
                            return
                        }
                    }

                    ; Create fingerprint directory if it doesn't exist
                    if (SubStr(dbPath, 1, 1) != "\" && SubStr(dbPath, 2, 1) != ":") {
                        dbPathFull := A_ScriptDir "\" dbPath
                    } else {
                        dbPathFull := dbPath
                    }

                    fptDir := dbPathFull "\fpt"
                    if (!DirExist(fptDir)) {
                        DirCreate(fptDir)
                        OutputDebug("Created fingerprint directory: " fptDir)
                    }

                    ; Generate filenames for template and image with new naming convention
                    rollNumber := EditRollSearch.Value
                    ; Check if we're in post-exam mode using the global variable
                    global g_isPostExamMode
                    examPhase := g_isPostExamMode ? "post" : "pre"

                    ; Use new naming convention: rollno_captured_fingerprint_[left|right]_[pre|post].[fpt|bmp]
                    templateFile := fptDir "\" rollNumber "_captured_fingerprint_left_" examPhase ".fpt"
                    imageFile := fptDir "\" rollNumber "_captured_fingerprint_left_" examPhase ".bmp"

                    ; Capture image with quality check
                    try {
                        imgBuf := g_fingerprintManager.CaptureImage(imageFile)
                        quality := g_fingerprintManager.GetImageQuality(imgBuf)

                        ; Display quality and update UI
                        OutputDebug("Fingerprint captured with quality: " quality)
                        try {
                            sbMain.Text := "Fingerprint quality: " quality
                        } catch as err {
                            OutputDebug("Error updating status bar: " err.Message)
                        }

                        ; Update fingerprint reader status since we successfully used it
                        try {
                            deviceInfo := g_fingerprintManager.GetDeviceInfo()
                            g_fingerprintReaderStatus := deviceInfo.DeviceModel
                            ErrorHandler.LogMessage("INFO", "Fingerprint reader status updated after successful capture: " g_fingerprintReaderStatus)
                            UpdateNetworkInfo()
                        } catch {
                            ; If we can't get device info but capture worked, it's still connected
                            g_fingerprintReaderStatus := "Connected"
                            ErrorHandler.LogMessage("INFO", "Fingerprint reader status set to Connected after successful capture")
                            UpdateNetworkInfo()
                        }
                    } catch Error as e {
                        OutputDebug("Error capturing fingerprint image: " e.Message)
                        try {
                            sbMain.Text := "Error capturing fingerprint: " e.Message
                        } catch as err {
                            OutputDebug("Error updating status bar: " err.Message)
                        }

                        ; Check if it's a device not found error
                        if (e.HasOwnProp("Extra") && e.Extra = 2) {
                            g_fingerprintReaderStatus := "Not Connected"
                            ErrorHandler.LogMessage("WARNING", "Fingerprint reader not connected during capture (error code 2)")
                            UpdateNetworkInfo()
                        }

                        ; Complete fingerprint capture with error to restore button state
                        CompleteFingerprintCapture(true, false, "Error capturing fingerprint: " e.Message)
                        return
                    }

                    ; Store the captured fingerprint path
                    if (FileExist(imageFile)) {
                        capturedFingerprintPath := imageFile
                    }

                    ; Check if quality is good enough
                    if (quality >= FingerprintCaptureThreshold) {
                        ; Save template
                        try {
                            g_fingerprintManager.CaptureTemplate(templateFile)
                        } catch Error as e {
                            OutputDebug("Error creating fingerprint template: " e.Message)
                            try {
                                sbMain.Text := "Error creating fingerprint template: " e.Message
                            } catch as err {
                                OutputDebug("Error updating status bar: " err.Message)
                            }
                            ; Complete fingerprint capture with error to restore button state
                            CompleteFingerprintCapture(true, false, "Error creating fingerprint template: " e.Message)
                            return
                        }

                        ; Update verification panel
                        VerifyFingerprintImage.Value := imageFile

                        ; Check if quality is high enough for auto-save
                        if (quality >= FingerprintAutoSaveThreshold) {
                            OutputDebug("Fingerprint quality " quality " exceeds AutoSaveThreshold " FingerprintAutoSaveThreshold ", auto-saving")

                            ; Auto-save based on FingerprintMode
                            if (FingerprintMode == "save") {
                                ; In save mode, mark as saved
                                FingerprintStatusValue.Text := "Saved"
                                FingerprintStatusValue.Opt("cGreen")
                                ButtonVerifyFingerprint.Enabled := false

                                ; Disable capture button when fingerprint verification is complete
                                if (IsSet(ButtonCaptureFingerprint) && IsObject(ButtonCaptureFingerprint)) {
                                    ButtonCaptureFingerprint.Enabled := false
                                    OutputDebug("Left fingerprint auto-save complete: Disabled capture button")
                                }

                                ; Update fingerprint status in database
                                try {
                                    if (g_isPostExamMode) {
                                        IniWrite("Verified", candidatesPath, rollNumber, "PostExamFingerprintStatus")
                                        OutputDebug("Auto-saved fingerprint: Updated PostExamFingerprintStatus to Verified")
                                    } else {
                                        IniWrite("Verified", candidatesPath, rollNumber, "FingerprintStatus")
                                        OutputDebug("Auto-saved fingerprint: Updated FingerprintStatus to Verified")
                                    }
                                } catch Error as e {
                                    OutputDebug("Error updating fingerprint status in database: " e.Message)
                                }

                                try {
                                    sbMain.Text := "High quality fingerprint (" quality ") auto-saved successfully"
                                } catch as err {
                                    OutputDebug("Error updating status bar: " err.Message)
                                }

                                ; Update button states based on new verification status
                                UpdateButtonStatesBasedOnVerificationStatus()
                            } else {
                                ; In compare mode, we still need to verify against registered template
                                ; But we'll mark it as high quality
                                FingerprintStatusValue.Text := "Ready (High Quality)"
                                FingerprintStatusValue.Opt("cBlue")
                                ButtonVerifyFingerprint.Enabled := true
                                try {
                                    sbMain.Text := "High quality fingerprint (" quality ") captured - Ready for verification"
                                } catch as err {
                                    OutputDebug("Error updating status bar: " err.Message)
                                }
                            }

                            ; Check if all verifications are complete
                            CheckAllVerifications()
                        } else {
                            ; Quality is good but not high enough for auto-save
                            FingerprintStatusValue.Text := "Ready"
                            FingerprintStatusValue.Opt("cBlue")
                            ButtonVerifyFingerprint.Enabled := true
                            try {
                                sbMain.Text := "Fingerprint captured successfully with quality: " quality
                            } catch as err {
                                OutputDebug("Error updating status bar: " err.Message)
                            }
                        }
                    } else {
                        try {
                            sbMain.Text := "Fingerprint quality too low (" quality "). Please try again."
                        } catch as err {
                            OutputDebug("Error updating status bar: " err.Message)
                        }
                    }

                } catch Error as e {
                    OutputDebug("Error capturing fingerprint: " e.Message)
                    CompleteFingerprintCapture(true, false, "Error capturing fingerprint: " e.Message)
                }
            } else {
                CompleteFingerprintCapture(true, false, "Fingerprint device not available or not initialized")
            }

            ; Always complete the capture state (in case of success, this was already called)
            CompleteFingerprintCapture(true, true)
        }
        else if (GuiCtrlObj == ButtonCaptureRightFingerprint) {
            ; Start fingerprint capture state management
            if (!StartFingerprintCapture(false)) {
                return  ; Another capture is in progress
            }

            ; Check if fingerprint manager is available
            if (IsObject(g_fingerprintManager)) {
                try {
                    ; Initialize the device if not already initialized
                    if (!SecuGenFingerprint.sgfplib) {
                        try {
                            g_fingerprintManager.Init()
                            OutputDebug("Fingerprint device initialized")
                        } catch Error as e {
                            OutputDebug("Error initializing fingerprint device: " e.Message)
                            sbMain.SetText("Error initializing fingerprint device: " e.Message)
                            ; Complete fingerprint capture with error to restore button state
                            CompleteFingerprintCapture(false, false, "Error initializing fingerprint device: " e.Message)
                            return
                        }
                    }

                    ; Create fingerprint directory if it doesn't exist
                    if (SubStr(dbPath, 1, 1) != "\" && SubStr(dbPath, 2, 1) != ":") {
                        dbPathFull := A_ScriptDir "\" dbPath
                    } else {
                        dbPathFull := dbPath
                    }

                    fptDir := dbPathFull "\fpt"
                    if (!DirExist(fptDir)) {
                        DirCreate(fptDir)
                        OutputDebug("Created fingerprint directory: " fptDir)
                    }

                    ; Generate filenames for template and image with new naming convention
                    rollNumber := EditRollSearch.Value
                    ; Check if we're in post-exam mode using the global variable
                    global g_isPostExamMode
                    examPhase := g_isPostExamMode ? "post" : "pre"

                    ; Use new naming convention: rollno_captured_fingerprint_[left|right]_[pre|post].[fpt|bmp]
                    templateFile := fptDir "\" rollNumber "_captured_fingerprint_right_" examPhase ".fpt"
                    imageFile := fptDir "\" rollNumber "_captured_fingerprint_right_" examPhase ".bmp"

                    ; Capture image with quality check
                    try {
                        imgBuf := g_fingerprintManager.CaptureImage(imageFile)
                        quality := g_fingerprintManager.GetImageQuality(imgBuf)

                        ; Display quality and update UI
                        OutputDebug("Right fingerprint captured with quality: " quality)
                        sbMain.SetText("Right fingerprint quality: " quality)

                        ; Update fingerprint reader status since we successfully used it
                        try {
                            deviceInfo := g_fingerprintManager.GetDeviceInfo()
                            g_fingerprintReaderStatus := deviceInfo.DeviceModel
                            ErrorHandler.LogMessage("INFO", "Fingerprint reader status updated after successful right capture: " g_fingerprintReaderStatus)
                            UpdateNetworkInfo()
                        } catch {
                            ; If we can't get device info but capture worked, it's still connected
                            g_fingerprintReaderStatus := "Connected"
                            ErrorHandler.LogMessage("INFO", "Fingerprint reader status set to Connected after successful right capture")
                            UpdateNetworkInfo()
                        }
                    } catch Error as e {
                        OutputDebug("Error capturing right fingerprint image: " e.Message)
                        sbMain.SetText("Error capturing right fingerprint: " e.Message)

                        ; Check if it's a device not found error
                        if (e.HasOwnProp("Extra") && e.Extra = 2) {
                            g_fingerprintReaderStatus := "Not Connected"
                            ErrorHandler.LogMessage("WARNING", "Fingerprint reader not connected during right capture (error code 2)")
                            UpdateNetworkInfo()
                        }

                        ; Complete fingerprint capture with error to restore button state
                        CompleteFingerprintCapture(false, false, "Error capturing right fingerprint: " e.Message)
                        return
                    }

                    ; Check if quality is good enough
                    if (quality >= FingerprintCaptureThreshold) {
                        ; Save template
                        try {
                            g_fingerprintManager.CaptureTemplate(templateFile)
                        } catch Error as e {
                            OutputDebug("Error creating right fingerprint template: " e.Message)
                            sbMain.SetText("Error creating right fingerprint template: " e.Message)
                            ; Complete fingerprint capture with error to restore button state
                            CompleteFingerprintCapture(false, false, "Error creating right fingerprint template: " e.Message)
                            return
                        }

                        ; Update verification panel
                        VerifyRightFingerprintImage.Value := imageFile

                        ; Check if quality is high enough for auto-save
                        if (quality >= FingerprintAutoSaveThreshold) {
                            OutputDebug("Right fingerprint quality " quality " exceeds AutoSaveThreshold " FingerprintAutoSaveThreshold ", auto-saving")

                            ; Auto-save based on FingerprintMode
                            if (FingerprintMode == "save") {
                                ; In save mode, mark as saved
                                RightFingerprintStatusValue.Text := "Saved"
                                RightFingerprintStatusValue.Opt("cGreen")
                                ButtonVerifyRightFingerprint.Enabled := false

                                ; Disable capture button when fingerprint verification is complete
                                if (IsSet(ButtonCaptureRightFingerprint) && IsObject(ButtonCaptureRightFingerprint)) {
                                    ButtonCaptureRightFingerprint.Enabled := false
                                    OutputDebug("Right fingerprint auto-save complete: Disabled capture button")
                                }

                                ; Update fingerprint status in database
                                try {
                                    if (g_isPostExamMode) {
                                        IniWrite("Verified", candidatesPath, rollNumber, "PostExamRightFingerprintStatus")
                                        OutputDebug("Auto-saved right fingerprint: Updated PostExamRightFingerprintStatus to Verified")
                                    } else {
                                        IniWrite("Verified", candidatesPath, rollNumber, "RightFingerprintStatus")
                                        OutputDebug("Auto-saved right fingerprint: Updated RightFingerprintStatus to Verified")
                                    }
                                } catch Error as e {
                                    OutputDebug("Error updating right fingerprint status in database: " e.Message)
                                }

                                ; Update button states based on new verification status
                                UpdateButtonStatesBasedOnVerificationStatus()

                                sbMain.SetText("High quality right fingerprint (" quality ") auto-saved successfully")
                            } else {
                                ; In compare mode, we still need to verify against registered template
                                ; But we'll mark it as high quality
                                RightFingerprintStatusValue.Text := "Ready (High Quality)"
                                RightFingerprintStatusValue.Opt("cBlue")
                                ButtonVerifyRightFingerprint.Enabled := true
                                sbMain.SetText("High quality right fingerprint (" quality ") captured - Ready for verification")
                            }

                            ; Check if all verifications are complete
                            CheckAllVerifications()
                        } else {
                            ; Quality is good but not high enough for auto-save
                            RightFingerprintStatusValue.Text := "Ready"
                            RightFingerprintStatusValue.Opt("cBlue")
                            ButtonVerifyRightFingerprint.Enabled := true
                            sbMain.SetText("Right fingerprint captured successfully with quality: " quality)
                        }
                    } else {
                        sbMain.SetText("Right fingerprint quality too low (" quality "). Please try again.")
                    }

                } catch Error as e {
                    OutputDebug("Error capturing right fingerprint: " e.Message)
                    CompleteFingerprintCapture(false, false, "Error capturing right fingerprint: " e.Message)
                }
            } else {
                CompleteFingerprintCapture(false, false, "Fingerprint device not available or not initialized")
            }

            ; Always complete the capture state (in case of success, this was already called)
            CompleteFingerprintCapture(false, true)
        }
        else if (GuiCtrlObj == ButtonCaptureSignature) {
            ; Only proceed if signature verification is enabled
            if (!SignatureVerificationEnabled) {
                sbMain.SetText("Signature verification is disabled in configuration")
                OutputDebug("ButtonCaptureSignature clicked but signature verification is disabled")
                return
            }

            ; Update status bar
            sbMain.SetText("Capturing signature...")

            try {
                ; Create temp directory if it doesn't exist
                tempDir := A_Temp "\wincbt_biometric"
                if (!DirExist(tempDir)) {
                    DirCreate(tempDir)
                    OutputDebug("Created temp directory: " tempDir)
                }

                ; Generate a unique filename using timestamp
                timestamp := FormatTime(, "yyyyMMdd_HHmmss")
                filename := tempDir "\captured_signature_" timestamp ".png"
                OutputDebug("Attempting to capture signature to: " filename)

                ; For now, we'll simulate signature capture by copying a sample file
                ; In a real implementation, this would interact with a signature pad
                try {
                    FileCopy("img\sample_signature.png", filename, true)
                    capturedSignaturePath := filename
                    OutputDebug("Successfully captured signature at: " capturedSignaturePath)

                    ; Update the signature image display
                    SignatureCaptureImage.Value := capturedSignaturePath

                    ; Update verification panel
                    SignatureVerifyImage.Value := capturedSignaturePath
                    SignatureStatusValue.Text := "Ready"
                    SignatureStatusValue.Opt("cBlue")
                    ButtonVerifySignature.Enabled := true

                    StatusCallback("Signature captured successfully - Ready for verification")
                } catch Error as e {
                    OutputDebug("Error capturing signature: " e.Message)
                    StatusCallback("Error capturing signature: " e.Message)
                }
            } catch Error as e {
                OutputDebug("Error in signature capture: " e.Message)
                sbMain.SetText("Error in signature capture: " e.Message)
            }
        }
        else if (GuiCtrlObj == ButtonVerifyPicture) {
            ; Skip if already verified
            if (PhotoStatusValue.Text == "Verified")
                return

            ; Update status bar
            sbMain.SetText("Verifying photo...")

            try {
                ; Get database paths using PathManager
                global capturedPhotoPath
                rollNumber := EditRollSearch.Value

                ; Get database file paths using PathManager
                candidatesPath := PathManager.GetDatabaseFilePath("Candidates")
                candidatesImgPath := PathManager.GetDatabaseSubPath("CandidateImages")

                ; Check if we have both a registered photo and a captured photo
                registeredPhotoPath := candidateData.Picture

                if (!FileExist(registeredPhotoPath)) {
                    sbMain.SetText("Error: Registered photo not found at " registeredPhotoPath)
                    return
                }

                if (!FileExist(capturedPhotoPath)) {
                    sbMain.SetText("Error: Captured photo not found at " capturedPhotoPath)
                    return
                }

                OutputDebug("Verifying photo - Registered: " registeredPhotoPath)
                OutputDebug("Verifying photo - Captured: " capturedPhotoPath)

                ; Determine verification mode
                if (PhotoVerificationMode == "Manual") {
                    ; In manual mode, automatically verify without asking for confirmation
                    PhotoStatusValue.Text := "Verified"
                    PhotoStatusValue.Opt("cGreen")
                    ButtonVerifyPicture.Enabled := false

                    ; Disable recapture button when photo verification is complete
                    if (IsSet(ButtonRecapturePhoto) && IsObject(ButtonRecapturePhoto)) {
                        ButtonRecapturePhoto.Enabled := false
                        OutputDebug("Manual photo verification complete: Disabled recapture button")
                    }

                    sbMain.SetText("Photo manually verified")

                    ; Save the captured photo to the candidate's folder
                    try {
                        ; Create the candidates image directory if it doesn't exist
                        if (!DirExist(candidatesImgPath)) {
                            DirCreate(candidatesImgPath)
                            OutputDebug("Created candidates image directory: " candidatesImgPath)
                        }

                        ; Check if we're in post-exam mode using the global variable
                        global g_isPostExamMode
                        examPhase := g_isPostExamMode ? "post" : "pre"

                        ; Save the captured photo using the new naming convention
                        capturedSavePath := candidatesImgPath rollNumber "_captured_photo_" examPhase ".jpg"
                        FileCopy(capturedPhotoPath, capturedSavePath, true)
                        OutputDebug("Saved captured photo to: " capturedSavePath)

                        ; Update photo status in database
                        if (g_isPostExamMode) {
                            IniWrite("Verified", candidatesPath, rollNumber, "PostExamPhotoStatus")
                            OutputDebug("Updated PostExamPhotoStatus to Verified (manual verification)")
                        } else {
                            IniWrite("Verified", candidatesPath, rollNumber, "PhotoStatus")
                            OutputDebug("Updated PhotoStatus to Verified (manual verification)")
                        }
                    } catch Error as e {
                        OutputDebug("Error saving captured photo: " e.Message)
                        sbMain.SetText("Warning: Could not save captured photo to database")
                    }
                } else {
                    ; In auto or both modes, use the VerifyPhoto function
                    verifyResult := VerifyPhoto(registeredPhotoPath, capturedPhotoPath)

                    OutputDebug("Photo verification result: " verifyResult.result
                               " with confidence " verifyResult.confidence)

                    if (verifyResult.result) {
                        ; Auto-verify if confidence is above threshold
                        if (verifyResult.confidence >= PhotoConfidenceThreshold) {
                            ; Automatic verification passed with high confidence
                            PhotoStatusValue.Text := "Verified"
                            PhotoStatusValue.Opt("cGreen")
                            ButtonVerifyPicture.Enabled := false

                            ; Disable recapture button when photo verification is complete
                            if (IsSet(ButtonRecapturePhoto) && IsObject(ButtonRecapturePhoto)) {
                                ButtonRecapturePhoto.Enabled := false
                                OutputDebug("High-confidence photo auto-verification complete: Disabled recapture button")
                            }

                            ; Format confidence value consistently
                            confidenceInt := Integer(verifyResult.confidence)
                            confidenceStr := String(confidenceInt)
                            sbMain.SetText("Photo auto-verified with " confidenceStr "% confidence: " verifyResult.message)

                            ; Update photo status in database immediately
                            try {
                                ; Use appropriate status field based on post-exam mode
                                if (g_isPostExamMode) {
                                    IniWrite("Verified", candidatesPath, rollNumber, "PostExamPhotoStatus")
                                    OutputDebug("Updated PostExamPhotoStatus to Verified (auto-verification in ButtonVerifyPicture)")
                                } else {
                                    IniWrite("Verified", candidatesPath, rollNumber, "PhotoStatus")
                                    OutputDebug("Updated PhotoStatus to Verified (auto-verification in ButtonVerifyPicture)")
                                }
                            } catch Error as e {
                                OutputDebug("Error updating photo status in database: " e.Message)
                            }
                        } else {
                            ; Confidence too low for auto-verification, but we'll auto-approve anyway

                            ; First, log the confidence value for debugging
                            confidenceValue := verifyResult.HasOwnProp("confidence") ? verifyResult.confidence : 0
                            confidenceInt := Integer(confidenceValue)
                            confidenceStr := String(confidenceInt)

                            OutputDebug("Auto-verification result is " (verifyResult.result ? "true" : "false")
                                       " but confidence " confidenceStr "% is below threshold " PhotoConfidenceThreshold "%")

                            ; Instead of requiring manual verification, automatically approve
                            PhotoStatusValue.Text := "Verified"
                            PhotoStatusValue.Opt("cGreen")
                            ButtonVerifyPicture.Enabled := false

                            ; Disable recapture button when photo verification is complete
                            if (IsSet(ButtonRecapturePhoto) && IsObject(ButtonRecapturePhoto)) {
                                ButtonRecapturePhoto.Enabled := false
                                OutputDebug("Auto photo verification complete (below threshold): Disabled recapture button")
                            }

                            ; Show the confidence in the status bar
                            sbMain.SetText("Photo auto-verified with " confidenceStr "% confidence (below threshold but auto-approved)")

                            ; Update photo status in database
                            try {
                                if (g_isPostExamMode) {
                                    IniWrite("Verified", candidatesPath, rollNumber, "PostExamPhotoStatus")
                                    OutputDebug("Updated PostExamPhotoStatus to Verified (auto-approved despite low confidence)")
                                } else {
                                    IniWrite("Verified", candidatesPath, rollNumber, "PhotoStatus")
                                    OutputDebug("Updated PhotoStatus to Verified (auto-approved despite low confidence)")
                                }
                            } catch Error as e {
                                OutputDebug("Error updating photo status in database: " e.Message)
                            }
                        }

                        ; If in "Both" mode and confidence is below threshold, prompt for manual verification
                        ; This section is now redundant and can be removed
                    } else {
                        ; Automatic verification failed
                        if (PhotoVerificationMode == "Both") {
                            ; In "Both" mode, automatically verify without asking for confirmation
                            PhotoStatusValue.Text := "Verified"
                            PhotoStatusValue.Opt("cGreen")
                            ButtonVerifyPicture.Enabled := false

                            ; Disable recapture button when photo verification is complete
                            if (IsSet(ButtonRecapturePhoto) && IsObject(ButtonRecapturePhoto)) {
                                ButtonRecapturePhoto.Enabled := false
                                OutputDebug("Both mode photo verification complete: Disabled recapture button")
                            }

                            ; Format confidence value consistently
                            confidenceInt := Integer(verifyResult.confidence)
                            confidenceStr := String(confidenceInt)
                            sbMain.SetText("Photo manually verified (overriding automatic failure with " confidenceStr "% confidence)")

                            ; Save the captured photo to the candidate's folder
                            try {
                                ; Create the candidates image directory if it doesn't exist
                                if (!DirExist(candidatesImgPath)) {
                                    DirCreate(candidatesImgPath)
                                    OutputDebug("Created candidates image directory: " candidatesImgPath)
                                }

                                ; Check if we're in post-exam mode using the global variable
                                global g_isPostExamMode
                                examPhase := g_isPostExamMode ? "post" : "pre"

                                ; Save the captured photo using the new naming convention
                                capturedSavePath := candidatesImgPath rollNumber "_captured_photo_" examPhase ".jpg"
                                FileCopy(capturedPhotoPath, capturedSavePath, true)
                                OutputDebug("Saved captured photo to: " capturedSavePath)
                            } catch Error as e {
                                OutputDebug("Error saving captured photo: " e.Message)
                                sbMain.SetText("Warning: Could not save captured photo to database")
                            }
                        } else {
                            ; In "Auto" mode, we'll auto-approve instead of failing
                            PhotoStatusValue.Text := "Verified"
                            PhotoStatusValue.Opt("cGreen")
                            ButtonVerifyPicture.Enabled := false

                            ; Disable recapture button when photo verification is complete
                            if (IsSet(ButtonRecapturePhoto) && IsObject(ButtonRecapturePhoto)) {
                                ButtonRecapturePhoto.Enabled := false
                                OutputDebug("Auto mode photo verification complete (auto-approved): Disabled recapture button")
                            }

                            ; Format confidence value consistently
                            confidenceInt := Integer(verifyResult.confidence)
                            confidenceStr := String(confidenceInt)
                            sbMain.SetText("Photo auto-verified with " confidenceStr "% confidence (auto-approved despite verification failure)")

                            ; Update photo status in database
                            try {
                                if (g_isPostExamMode) {
                                    IniWrite("Verified", candidatesPath, rollNumber, "PostExamPhotoStatus")
                                    OutputDebug("Updated PostExamPhotoStatus to Verified (auto-approved despite verification failure)")
                                } else {
                                    IniWrite("Verified", candidatesPath, rollNumber, "PhotoStatus")
                                    OutputDebug("Updated PhotoStatus to Verified (auto-approved despite verification failure)")
                                }
                            } catch Error as e {
                                OutputDebug("Error updating photo status in database: " e.Message)
                            }
                        }
                    }
                }

                ; If verification was successful, update the photo status in database
                if (PhotoStatusValue.Text == "Verified") {
                    try {
                        if (g_isPostExamMode) {
                            IniWrite("Verified", candidatesPath, rollNumber, "PostExamPhotoStatus")
                            OutputDebug("Updated PostExamPhotoStatus to Verified")
                        } else {
                            IniWrite("Verified", candidatesPath, rollNumber, "PhotoStatus")
                            OutputDebug("Updated PhotoStatus to Verified")
                        }
                    } catch Error as e {
                        OutputDebug("Error updating photo status in database: " e.Message)
                    }

                    ; Update button states based on new verification status
                    UpdateButtonStatesBasedOnVerificationStatus()

                    ; If signature verification is disabled, check if fingerprint is also verified
                    if (!SignatureVerificationEnabled) {
                        if (FingerprintStatusValue.Text == "Verified" || FingerprintStatusValue.Text == "Saved" || FingerprintStatusValue.Text == "Skipped") {
                            OutputDebug("Photo verified and fingerprint verified/saved/skipped with signature verification disabled")
                            OutputDebug("Checking all verifications to update status")
                        }
                    }
                }

                ; Check if all verifications are complete
                CheckAllVerifications()

            } catch Error as e {
                OutputDebug("Error verifying photo: " e.Message)
                sbMain.SetText("Error verifying photo: " e.Message)
            }
        }
        else if (GuiCtrlObj == ButtonVerifyFingerprint) {
            ; Update status bar
            sbMain.SetText("Verifying fingerprint...")

            ; Check if fingerprint manager is available
            if (IsObject(g_fingerprintManager)) {
                try {
                    ; Get database paths
                    global dbPath
                    rollNumber := EditRollSearch.Value

                    ; Convert relative to absolute path if needed
                    if (SubStr(dbPath, 1, 1) != "\" && SubStr(dbPath, 2, 1) != ":") {
                        dbPathFull := A_ScriptDir "\" dbPath
                    } else {
                        dbPathFull := dbPath
                    }

                    ; Get database file paths using PathManager
                    candidatesPath := PathManager.GetDatabaseFilePath("Candidates")
                    candidatesImgPath := PathManager.GetDatabaseSubPath("CandidateImages")

                    ; Check if we're in post-exam mode using the global variable
                    global g_isPostExamMode
                    examPhase := g_isPostExamMode ? "post" : "pre"

                    ; Use PathManager to get fingerprint template path
                    templateFile := PathManager.GetFingerprintTemplatePath(rollNumber, "left", examPhase)

                    if (FingerprintMode == "save") {
                        ; In save mode, just mark as saved
                        if (FileExist(templateFile)) {
                            FingerprintStatusValue.Text := "Saved"
                            FingerprintStatusValue.Opt("cGreen")
                            sbMain.SetText("Fingerprint saved successfully")

                            ; Disable capture button when fingerprint verification is complete
                            if (IsSet(ButtonCaptureFingerprint) && IsObject(ButtonCaptureFingerprint)) {
                                ButtonCaptureFingerprint.Enabled := false
                                OutputDebug("Left fingerprint manual save complete: Disabled capture button")
                            }

                            ; Disable save button when fingerprint save is complete
                            if (IsSet(ButtonVerifyFingerprint) && IsObject(ButtonVerifyFingerprint)) {
                                ButtonVerifyFingerprint.Enabled := false
                                OutputDebug("Left fingerprint manual save complete: Disabled save button")
                            }

                            ; Update fingerprint status in database
                            IniWrite("Verified", candidatesPath, rollNumber, "FingerprintStatus")

                            ; Update button states based on new verification status
                            UpdateButtonStatesBasedOnVerificationStatus()

                            ; If signature verification is disabled, check if photo is also verified
                            if (!SignatureVerificationEnabled) {
                                if (PhotoStatusValue.Text == "Verified") {
                                    OutputDebug("Fingerprint saved and photo verified with signature verification disabled")
                                    OutputDebug("Checking all verifications to update status")
                                }
                            }
                        } else {
                            sbMain.SetText("Error: Fingerprint template file not found")
                        }
                    } else {
                        ; In compare mode, verify against existing template
                        ; First check if there's a registered fingerprint
                        ; For comparison, we need to check if we're comparing against a pre-exam fingerprint
                        ; In post-exam mode, we compare against the pre-exam fingerprint
                        comparePhase := "pre"  ; Always compare against pre-exam fingerprint
                        registeredTemplate := fptDir "\" rollNumber "_captured_fingerprint_left_" comparePhase ".fpt"

                        if (FileExist(registeredTemplate) && FileExist(templateFile)) {
                            ; Check if this is a high-quality fingerprint that was auto-marked
                            isHighQuality := (FingerprintStatusValue.Value == "Ready (High Quality)")

                            ; Compare the templates
                            try {
                                ; Use a higher security level for regular quality fingerprints
                                ; and a slightly lower one for high-quality fingerprints
                                securityLevel := isHighQuality ? 2 : 3
                                matchResult := g_fingerprintManager.MatchTemplates(templateFile, registeredTemplate, securityLevel)

                                if (isHighQuality) {
                                    OutputDebug("Using security level " securityLevel " for high-quality fingerprint verification")
                                }
                            } catch Error as e {
                                OutputDebug("Error matching fingerprint templates: " e.Message)
                                sbMain.SetText("Error matching fingerprint templates: " e.Message)
                                return
                            }

                            if (matchResult) {
                                FingerprintStatusValue.Text := "Verified"
                                FingerprintStatusValue.Opt("cGreen")

                                ; Disable capture button when fingerprint verification is complete
                                if (IsSet(ButtonCaptureFingerprint) && IsObject(ButtonCaptureFingerprint)) {
                                    ButtonCaptureFingerprint.Enabled := false
                                    OutputDebug("Left fingerprint manual verification complete: Disabled capture button")
                                }

                                ; Disable verify button when fingerprint verification is complete
                                if (IsSet(ButtonVerifyFingerprint) && IsObject(ButtonVerifyFingerprint)) {
                                    ButtonVerifyFingerprint.Enabled := false
                                    OutputDebug("Left fingerprint manual verification complete: Disabled verify button")
                                }

                                if (isHighQuality) {
                                    sbMain.SetText("High-quality fingerprint verified successfully")
                                } else {
                                    sbMain.SetText("Fingerprint verified successfully")
                                }

                                ; Update fingerprint status in database
                                if (g_isPostExamMode) {
                                    IniWrite("Verified", candidatesPath, rollNumber, "PostExamFingerprintStatus")
                                    OutputDebug("Updated PostExamFingerprintStatus to Verified")
                                } else {
                                    IniWrite("Verified", candidatesPath, rollNumber, "FingerprintStatus")
                                    OutputDebug("Updated FingerprintStatus to Verified")
                                }

                                ; Update button states based on new verification status
                                UpdateButtonStatesBasedOnVerificationStatus()

                                ; If signature verification is disabled, check if photo is also verified
                                if (!SignatureVerificationEnabled) {
                                    if (PhotoStatusValue.Text == "Verified") {
                                        OutputDebug("Fingerprint verified and photo verified with signature verification disabled")
                                        OutputDebug("Checking all verifications to update status")
                                    }
                                }
                            } else {
                                FingerprintStatusValue.Text := "Failed"
                                FingerprintStatusValue.Opt("cRed")

                                if (isHighQuality) {
                                    sbMain.SetText("High-quality fingerprint verification failed - does not match registered print")
                                } else {
                                    sbMain.SetText("Fingerprint verification failed - does not match registered print")
                                }
                            }
                        } else if (AllowMissingFingerprint) {
                            ; Allow skipping if no registered fingerprint
                            if (MsgBox("No registered fingerprint found. Skip verification?", "Missing Fingerprint", "YesNo") == "Yes") {
                                FingerprintStatusValue.Text := "Skipped"
                                FingerprintStatusValue.Opt("cBlue")
                                sbMain.SetText("Fingerprint verification skipped")
                            }
                        } else {
                            sbMain.SetText("Error: Registered fingerprint template not found")
                        }
                    }

                    ; Check if all verifications are complete
                    CheckAllVerifications()

                } catch Error as e {
                    OutputDebug("Error verifying fingerprint: " e.Message)
                    sbMain.SetText("Error verifying fingerprint: " e.Message)
                }
            } else {
                sbMain.SetText("Fingerprint device not available or not initialized")
            }
        }
        else if (GuiCtrlObj == ButtonVerifyRightFingerprint) {
            ; Update status bar
            sbMain.SetText("Verifying right fingerprint...")

            ; Check if fingerprint manager is available
            if (IsObject(g_fingerprintManager)) {
                try {
                    ; Get database paths
                    global dbPath
                    rollNumber := EditRollSearch.Value

                    ; Convert relative to absolute path if needed
                    if (SubStr(dbPath, 1, 1) != "\" && SubStr(dbPath, 2, 1) != ":") {
                        dbPathFull := A_ScriptDir "\" dbPath
                    } else {
                        dbPathFull := dbPath
                    }

                    ; Get database file paths using PathManager
                    candidatesPath := PathManager.GetDatabaseFilePath("Candidates")
                    candidatesImgPath := PathManager.GetDatabaseSubPath("CandidateImages")

                    ; Check if we're in post-exam mode using the global variable
                    global g_isPostExamMode
                    examPhase := g_isPostExamMode ? "post" : "pre"

                    ; Use PathManager to get fingerprint template path
                    templateFile := PathManager.GetFingerprintTemplatePath(rollNumber, "right", examPhase)

                    if (FingerprintMode == "save") {
                        ; In save mode, just mark as saved
                        if (FileExist(templateFile)) {
                            RightFingerprintStatusValue.Text := "Saved"
                            RightFingerprintStatusValue.Opt("cGreen")
                            sbMain.SetText("Right fingerprint saved successfully")

                            ; Disable capture button when fingerprint verification is complete
                            if (IsSet(ButtonCaptureRightFingerprint) && IsObject(ButtonCaptureRightFingerprint)) {
                                ButtonCaptureRightFingerprint.Enabled := false
                                OutputDebug("Right fingerprint manual save complete: Disabled capture button")
                            }

                            ; Disable save button when fingerprint save is complete
                            if (IsSet(ButtonVerifyRightFingerprint) && IsObject(ButtonVerifyRightFingerprint)) {
                                ButtonVerifyRightFingerprint.Enabled := false
                                OutputDebug("Right fingerprint manual save complete: Disabled save button")
                            }

                            ; Update fingerprint status in database
                            if (g_isPostExamMode) {
                                IniWrite("Verified", candidatesPath, rollNumber, "PostExamRightFingerprintStatus")
                                OutputDebug("Updated PostExamRightFingerprintStatus to Verified")
                            } else {
                                IniWrite("Verified", candidatesPath, rollNumber, "RightFingerprintStatus")
                                OutputDebug("Updated RightFingerprintStatus to Verified")
                            }

                            ; Update button states based on new verification status
                            UpdateButtonStatesBasedOnVerificationStatus()

                            ; If signature verification is disabled, check if photo is also verified
                            if (!SignatureVerificationEnabled) {
                                if (PhotoStatusValue.Text == "Verified") {
                                    OutputDebug("Right fingerprint saved and photo verified with signature verification disabled")
                                    OutputDebug("Checking all verifications to update status")
                                }
                            }
                        } else {
                            sbMain.SetText("Error: Right fingerprint template file not found")
                        }
                    } else {
                        ; In compare mode, verify against existing template
                        ; First check if there's a registered fingerprint
                        ; For comparison, we need to check if we're comparing against a pre-exam fingerprint
                        ; In post-exam mode, we compare against the pre-exam fingerprint
                        comparePhase := "pre"  ; Always compare against pre-exam fingerprint
                        registeredTemplate := fptDir "\" rollNumber "_captured_fingerprint_right_" comparePhase ".fpt"

                        if (FileExist(registeredTemplate) && FileExist(templateFile)) {
                            ; Check if this is a high-quality fingerprint that was auto-marked
                            isHighQuality := (RightFingerprintStatusValue.Text == "Ready (High Quality)")

                            ; Compare the templates
                            try {
                                ; Use a higher security level for regular quality fingerprints
                                ; and a slightly lower one for high-quality fingerprints
                                securityLevel := isHighQuality ? 2 : 3
                                matchResult := g_fingerprintManager.MatchTemplates(templateFile, registeredTemplate, securityLevel)

                                if (isHighQuality) {
                                    OutputDebug("Using security level " securityLevel " for high-quality right fingerprint verification")
                                }
                            } catch Error as e {
                                OutputDebug("Error matching right fingerprint templates: " e.Message)
                                sbMain.SetText("Error matching right fingerprint templates: " e.Message)
                                return
                            }

                            if (matchResult) {
                                RightFingerprintStatusValue.Text := "Verified"
                                RightFingerprintStatusValue.Opt("cGreen")

                                ; Disable capture button when fingerprint verification is complete
                                if (IsSet(ButtonCaptureRightFingerprint) && IsObject(ButtonCaptureRightFingerprint)) {
                                    ButtonCaptureRightFingerprint.Enabled := false
                                    OutputDebug("Right fingerprint manual verification complete: Disabled capture button")
                                }

                                ; Disable verify button when fingerprint verification is complete
                                if (IsSet(ButtonVerifyRightFingerprint) && IsObject(ButtonVerifyRightFingerprint)) {
                                    ButtonVerifyRightFingerprint.Enabled := false
                                    OutputDebug("Right fingerprint manual verification complete: Disabled verify button")
                                }

                                if (isHighQuality) {
                                    sbMain.SetText("High-quality right fingerprint verified successfully")
                                } else {
                                    sbMain.SetText("Right fingerprint verified successfully")
                                }

                                ; Update fingerprint status in database
                                if (g_isPostExamMode) {
                                    IniWrite("Verified", candidatesPath, rollNumber, "PostExamRightFingerprintStatus")
                                    OutputDebug("Updated PostExamRightFingerprintStatus to Verified")
                                } else {
                                    IniWrite("Verified", candidatesPath, rollNumber, "RightFingerprintStatus")
                                    OutputDebug("Updated RightFingerprintStatus to Verified")
                                }

                                ; Update button states based on new verification status
                                UpdateButtonStatesBasedOnVerificationStatus()

                                ; If signature verification is disabled, check if photo is also verified
                                if (!SignatureVerificationEnabled) {
                                    if (PhotoStatusValue.Text == "Verified") {
                                        OutputDebug("Right fingerprint verified and photo verified with signature verification disabled")
                                        OutputDebug("Checking all verifications to update status")
                                    }
                                }
                            } else {
                                RightFingerprintStatusValue.Text := "Failed"
                                RightFingerprintStatusValue.Opt("cRed")
                                sbMain.SetText("Right fingerprint verification failed - fingerprints do not match")
                            }
                        } else {
                            ; If we're in post-exam mode and there's no pre-exam fingerprint, we can't verify
                            if (g_isPostExamMode && !FileExist(registeredTemplate)) {
                                sbMain.SetText("No pre-exam right fingerprint found for comparison")
                            } else if (!FileExist(templateFile)) {
                                sbMain.SetText("No captured right fingerprint found")
                            } else {
                                sbMain.SetText("Error: Required right fingerprint template files not found")
                            }
                        }
                    }

                    ; Check if all verifications are complete
                    CheckAllVerifications()

                } catch Error as e {
                    OutputDebug("Error verifying right fingerprint: " e.Message)
                    sbMain.SetText("Error verifying right fingerprint: " e.Message)
                }
            } else {
                sbMain.SetText("Fingerprint device not available or not initialized")
            }
        }
        else if (GuiCtrlObj == ButtonVerifySignature) {
            ; Only proceed if signature verification is enabled
            if (!SignatureVerificationEnabled) {
                ; If signature verification is disabled, we shouldn't even get here
                ; But just in case, inform the user and do nothing
                sbMain.SetText("Signature verification is disabled in configuration")
                OutputDebug("ButtonVerifySignature clicked but signature verification is disabled")
                return
            }

            ; Update status bar
            sbMain.SetText("Verifying signature...")

            try {
                ; Get database paths using PathManager
                global capturedSignaturePath
                rollNumber := EditRollSearch.Value

                ; Get database file paths using PathManager
                candidatesPath := PathManager.GetDatabaseFilePath("Candidates")
                candidatesImgPath := PathManager.GetDatabaseSubPath("CandidateImages")

                ; Check if we have both a registered signature and a captured signature
                registeredSignaturePath := candidateData.Signature

                if (!FileExist(registeredSignaturePath)) {
                    sbMain.SetText("Error: Registered signature not found at " registeredSignaturePath)
                    return
                }

                if (!FileExist(capturedSignaturePath)) {
                    sbMain.SetText("Error: Captured signature not found at " capturedSignaturePath)
                    return
                }

                OutputDebug("Verifying signature - Registered: " registeredSignaturePath)
                OutputDebug("Verifying signature - Captured: " capturedSignaturePath)

                ; Determine verification mode
                if (SignatureVerificationMode == "Manual") {
                    ; In manual mode, show both signatures and ask for confirmation
                    if (MsgBox("Do the signatures match?`n`nRegistered signature is on the left.`nCaptured signature is on the right.",
                              "Manual Signature Verification", "YesNo Icon!") == "Yes") {
                        ; User confirmed match
                        SignatureStatusValue.Text := "Verified"
                        SignatureStatusValue.Opt("cGreen")
                        sbMain.SetText("Signature manually verified")

                        ; Save the captured signature to the candidate's folder
                        try {
                            ; Create the candidates image directory if it doesn't exist
                            if (!DirExist(candidatesImgPath)) {
                                DirCreate(candidatesImgPath)
                                OutputDebug("Created candidates image directory: " candidatesImgPath)
                            }

                            ; Check if we're in post-exam mode using the global variable
                            global g_isPostExamMode
                            examPhase := g_isPostExamMode ? "post" : "pre"

                            ; Save the captured signature using the new naming convention
                            capturedSavePath := candidatesImgPath rollNumber "_captured_signature_" examPhase ".jpg"
                            FileCopy(capturedSignaturePath, capturedSavePath, true)
                            OutputDebug("Saved captured signature to: " capturedSavePath)
                        } catch Error as e {
                            OutputDebug("Error saving captured signature: " e.Message)
                            sbMain.SetText("Warning: Could not save captured signature to database")
                        }
                    } else {
                        ; User rejected match
                        SignatureStatusValue.Text := "Failed"
                        SignatureStatusValue.Opt("cRed")
                        sbMain.SetText("Signature verification failed - manual rejection")
                    }
                } else {
                    ; In auto or both modes, use the VerifySignature function
                    verifyResult := VerifySignature(registeredSignaturePath, capturedSignaturePath)

                    OutputDebug("Signature verification result: " verifyResult.result
                               " with confidence " verifyResult.confidence)

                    if (verifyResult.result) {
                        ; Automatic verification passed
                        SignatureStatusValue.Text := "Verified"
                        SignatureStatusValue.Opt("cGreen")
                        sbMain.SetText("Signature verified with " verifyResult.confidence "% confidence: " verifyResult.message)

                        ; Save the captured signature to the candidate's folder
                        try {
                            ; Create the candidates image directory if it doesn't exist
                            if (!DirExist(candidatesImgPath)) {
                                DirCreate(candidatesImgPath)
                                OutputDebug("Created candidates image directory: " candidatesImgPath)
                            }

                            ; Check if we're in post-exam mode using the global variable
                            global g_isPostExamMode
                            examPhase := g_isPostExamMode ? "post" : "pre"

                            ; Save the captured signature using the new naming convention
                            capturedSavePath := candidatesImgPath rollNumber "_captured_signature_" examPhase ".jpg"
                            FileCopy(capturedSignaturePath, capturedSavePath, true)
                            OutputDebug("Saved captured signature to: " capturedSavePath)
                        } catch Error as e {
                            OutputDebug("Error saving captured signature: " e.Message)
                            sbMain.SetText("Warning: Could not save captured signature to database")
                        }

                        ; If in "Both" mode and confidence is below threshold, prompt for manual verification
                        if (SignatureVerificationMode == "Both" && verifyResult.confidence < SignatureConfidenceThreshold) {
                            if (MsgBox("Automatic verification passed with " verifyResult.confidence "% confidence.`n`n"
                                      "Do you want to manually verify the signatures?`n`n"
                                      "Registered signature is on the left.`nCaptured signature is on the right.",
                                      "Manual Signature Verification", "YesNo Icon!") == "No") {
                                ; User rejected the match despite automatic verification
                                SignatureStatusValue.Text := "Failed"
                                SignatureStatusValue.Opt("cRed")
                                sbMain.SetText("Signature verification failed - manual rejection")
                            }
                        }
                    } else {
                        ; Automatic verification failed
                        if (SignatureVerificationMode == "Both") {
                            ; In "Both" mode, allow manual override
                            if (MsgBox("Automatic verification failed with " verifyResult.confidence "% confidence.`n`n"
                                      "Do you want to manually verify the signatures?`n`n"
                                      "Registered signature is on the left.`nCaptured signature is on the right.",
                                      "Manual Signature Verification", "YesNo Icon!") == "Yes") {
                                ; User manually verified
                                SignatureStatusValue.Text := "Verified"
                                SignatureStatusValue.Opt("cGreen")
                                sbMain.SetText("Signature manually verified (overriding automatic failure)")

                                ; Save the captured signature to the candidate's folder
                                try {
                                    ; Create the candidates image directory if it doesn't exist
                                    if (!DirExist(candidatesImgPath)) {
                                        DirCreate(candidatesImgPath)
                                        OutputDebug("Created candidates image directory: " candidatesImgPath)
                                    }

                                    ; Check if we're in post-exam mode using the global variable
                                    global g_isPostExamMode
                                    examPhase := g_isPostExamMode ? "post" : "pre"

                                    ; Save the captured signature using the new naming convention
                                    capturedSavePath := candidatesImgPath rollNumber "_captured_signature_" examPhase ".jpg"
                                    FileCopy(capturedSignaturePath, capturedSavePath, true)
                                    OutputDebug("Saved captured signature to: " capturedSavePath)
                                } catch Error as e {
                                    OutputDebug("Error saving captured signature: " e.Message)
                                    sbMain.SetText("Warning: Could not save captured signature to database")
                                }
                            } else {
                                ; User confirmed the failure
                                SignatureStatusValue.Text := "Failed"
                                SignatureStatusValue.Opt("cRed")
                                sbMain.SetText("Signature verification failed - " verifyResult.message)
                            }
                        } else {
                            ; In "Auto" mode, just fail
                            SignatureStatusValue.Text := "Failed"
                            SignatureStatusValue.Opt("cRed")
                            sbMain.SetText("Signature verification failed - " verifyResult.message)
                        }
                    }
                }

                ; If verification was successful, update the signature status in database
                if (SignatureStatusValue.Text == "Verified") {
                    try {
                        if (g_isPostExamMode) {
                            IniWrite("Verified", candidatesPath, rollNumber, "PostExamSignatureStatus")
                            OutputDebug("Updated PostExamSignatureStatus to Verified")
                        } else {
                            IniWrite("Verified", candidatesPath, rollNumber, "SignatureStatus")
                            OutputDebug("Updated SignatureStatus to Verified")
                        }
                    } catch Error as e {
                        OutputDebug("Error updating signature status in database: " e.Message)
                    }
                }

                ; Check if all verifications are complete
                CheckAllVerifications()

            } catch Error as e {
                OutputDebug("Error verifying signature: " e.Message)
                sbMain.SetText("Error verifying signature: " e.Message)
            }
        }
        else if (GuiCtrlObj == EnableRightThumbCheckbox) {
            ; Handle the right thumb checkbox
            rollNumber := EditRollSearch.Value

            if (EnableRightThumbCheckbox.Value) {
                ; If right thumb is enabled, disable left thumb and enable right thumb
                ButtonCaptureFingerprint.Enabled := false
                ButtonVerifyFingerprint.Enabled := false

                ; Set left thumbprint status to "Disabled" with gray color
                FingerprintStatusValue.Text := "Disabled"
                FingerprintStatusValue.Opt("cGray")

                ; Enable right thumb controls regardless of global setting
                ButtonCaptureRightFingerprint.Enabled := true

                ; Reset right thumbprint status to prepare for capture
                RightFingerprintStatusValue.Text := "-"
                RightFingerprintStatusValue.Opt("cGray")
                ButtonVerifyRightFingerprint.Enabled := false

                ; Show the special case indicator when right thumb is selected
                ; This applies to both regular candidates and special candidates
                SpecialCaseIndicator.Visible := true

                ; Log the special case (but don't store in candidates.ini)
                logMessage := FormatTime(, "yyyy-MM-dd HH:mm:ss") " - Temporary special case accommodation: Using right thumb instead for candidate " rollNumber
                FileAppend(logMessage "`n", "logs\application.log")
                OutputDebug("Right thumb enabled for candidate: " rollNumber)

                sbMain.SetText("Right thumb enabled - Left thumb verification will be skipped")

                ; Check if all verifications are complete
                CheckAllVerifications()
            } else {
                ; If right thumb is disabled, enable left thumb and disable right thumb
                ButtonCaptureFingerprint.Enabled := true

                ; Reset left thumb status
                FingerprintStatusValue.Text := "-"
                FingerprintStatusValue.Opt("cGray")

                ; Reset right thumb status and disable controls if global setting is off
                RightFingerprintStatusValue.Text := "-"
                RightFingerprintStatusValue.Opt("cGray")
                ButtonVerifyRightFingerprint.Enabled := false

                ; Only disable the capture button if global setting is off
                if (!RightThumbprintVerificationEnabled) {
                    ButtonCaptureRightFingerprint.Enabled := false
                    RightFingerprintStatusValue.Text := "Disabled"
                }

                ; Hide the special case indicator when left thumb is selected in single thumb mode
                ; For special candidates, this ensures the indicator is only shown when right thumb is used
                if (!RightThumbprintVerificationEnabled) {
                    SpecialCaseIndicator.Visible := false
                }

                ; Log the change
                logMessage := FormatTime(, "yyyy-MM-dd HH:mm:ss") " - Temporary special case accommodation removed for candidate " rollNumber
                FileAppend(logMessage "`n", "logs\application.log")
                OutputDebug("Right thumb disabled for candidate: " rollNumber)

                sbMain.SetText("Right thumb disabled - Left thumb verification required")

                ; Check if all verifications are complete
                CheckAllVerifications()
            }

            ; Save thumb preference for non-special candidates in pre-exam mode
            if (!g_isPostExamMode && rollNumber != "" && rollNumber != "Candidate Not Found") {
                ; Check if this is a non-special candidate
                try {
                    candidateData := LoadCandidateData(rollNumber)
                    if (candidateData.Special != "1") {
                        SaveThumbPreferenceForNonSpecialCandidate(rollNumber)
                    }
                } catch {
                    ; Ignore errors during preference saving
                }
            }
        }
        else if (GuiCtrlObj == SingleThumbCheckbox) {
            ; Handle the single thumb checkbox
            rollNumber := EditRollSearch.Value

            if (SingleThumbCheckbox.Value) {
                ; If single thumb is checked, show and enable the radio buttons
                LeftThumbRadio.Visible := true
                RightThumbRadio.Visible := true
                LeftThumbRadio.Enabled := true
                RightThumbRadio.Enabled := true

                ; Default to left thumb
                LeftThumbRadio.Value := true
                RightThumbRadio.Value := false

                ; Show the special case indicator
                SpecialCaseIndicator.Visible := true

                ; Hide the "Use Right Thumb Instead" checkbox to avoid conflicting UI
                EnableRightThumbCheckbox.Visible := false
                EnableRightThumbCheckbox.Value := false

                ; Log the special case (but don't store in candidates.ini)
                logMessage := FormatTime(, "yyyy-MM-dd HH:mm:ss") " - Temporary special case accommodation: SingleThumbOnly for candidate " rollNumber
                FileAppend(logMessage "`n", "logs\application.log")
                OutputDebug("Single thumb mode enabled for candidate: " rollNumber)

                ; Enable left thumb controls initially (since left thumb is selected by default)
                ButtonCaptureFingerprint.Enabled := true
                FingerprintStatusValue.Text := "-"
                FingerprintStatusValue.Opt("cGray")

                ; Disable right thumb controls initially
                ButtonCaptureRightFingerprint.Enabled := false
                RightFingerprintStatusValue.Text := "Disabled"
                RightFingerprintStatusValue.Opt("cGray")

                sbMain.SetText("Single thumb mode enabled - Please select which thumb is available")
            } else {
                ; If single thumb is unchecked, hide the radio buttons
                LeftThumbRadio.Visible := false
                RightThumbRadio.Visible := false

                ; Only show the "Use Right Thumb Instead" checkbox in single thumb mode
                EnableRightThumbCheckbox.Visible := !RightThumbprintVerificationEnabled

                ; Reset thumb controls to default state
                ButtonCaptureFingerprint.Enabled := true
                FingerprintStatusValue.Text := "-"
                FingerprintStatusValue.Opt("cGray")

                ButtonCaptureRightFingerprint.Enabled := RightThumbprintVerificationEnabled
                RightFingerprintStatusValue.Text := RightThumbprintVerificationEnabled ? "-" : "Disabled"
                RightFingerprintStatusValue.Opt("cGray")

                ; Hide the special case indicator
                SpecialCaseIndicator.Visible := false

                ; Log the change
                logMessage := FormatTime(, "yyyy-MM-dd HH:mm:ss") " - Temporary special case accommodation removed for candidate " rollNumber
                FileAppend(logMessage "`n", "logs\application.log")
                OutputDebug("Normal dual thumb mode enabled for candidate: " rollNumber)

                sbMain.SetText("Normal dual thumb mode enabled")
            }

            ; Check if all verifications are complete
            CheckAllVerifications()

            ; Save thumb preference for non-special candidates in pre-exam mode
            if (!g_isPostExamMode && rollNumber != "" && rollNumber != "Candidate Not Found") {
                ; Check if this is a non-special candidate
                try {
                    candidateData := LoadCandidateData(rollNumber)
                    if (candidateData.Special != "1") {
                        SaveThumbPreferenceForNonSpecialCandidate(rollNumber)
                    }
                } catch {
                    ; Ignore errors during preference saving
                }
            }
        }
        else if (GuiCtrlObj == LeftThumbRadio) {
            ; Handle the left thumb radio button
            if (LeftThumbRadio.Value) {
                ; Enable left thumb controls
                ButtonCaptureFingerprint.Enabled := true
                FingerprintStatusValue.Text := "-"
                FingerprintStatusValue.Opt("cGray")

                ; Disable right thumb controls
                ButtonCaptureRightFingerprint.Enabled := false
                RightFingerprintStatusValue.Text := "Disabled"
                RightFingerprintStatusValue.Opt("cGray")

                sbMain.SetText("Left thumb selected for capture")
                OutputDebug("Left thumb selected for candidate: " EditRollSearch.Value)

                ; Check if all verifications are complete
                CheckAllVerifications()

                ; Save thumb preference for non-special candidates in pre-exam mode
                rollNumber := EditRollSearch.Value
                if (!g_isPostExamMode && rollNumber != "" && rollNumber != "Candidate Not Found") {
                    ; Check if this is a non-special candidate
                    try {
                        candidateData := LoadCandidateData(rollNumber)
                        if (candidateData.Special != "1") {
                            SaveThumbPreferenceForNonSpecialCandidate(rollNumber)
                        }
                    } catch {
                        ; Ignore errors during preference saving
                    }
                }
            }
        }
        else if (GuiCtrlObj == RightThumbRadio) {
            ; Handle the right thumb radio button
            if (RightThumbRadio.Value) {
                ; Disable left thumb controls
                ButtonCaptureFingerprint.Enabled := false
                FingerprintStatusValue.Text := "Disabled"
                FingerprintStatusValue.Opt("cGray")

                ; Enable right thumb controls
                ButtonCaptureRightFingerprint.Enabled := true
                RightFingerprintStatusValue.Text := "-"
                RightFingerprintStatusValue.Opt("cGray")

                sbMain.SetText("Right thumb selected for capture")
                OutputDebug("Right thumb selected for candidate: " EditRollSearch.Value)

                ; Check if all verifications are complete
                CheckAllVerifications()

                ; Save thumb preference for non-special candidates in pre-exam mode
                rollNumber := EditRollSearch.Value
                if (!g_isPostExamMode && rollNumber != "" && rollNumber != "Candidate Not Found") {
                    ; Check if this is a non-special candidate
                    try {
                        candidateData := LoadCandidateData(rollNumber)
                        if (candidateData.Special != "1") {
                            SaveThumbPreferenceForNonSpecialCandidate(rollNumber)
                        }
                    } catch {
                        ; Ignore errors during preference saving
                    }
                }
            }
        }
        else if (GuiCtrlObj == ButtonAssignSeat) {
            ; Get current candidate information
            rollNumber := EditRollSearch.Value
            candidateName := CandidateNameText.Text
            examId := candidateData.ExamID  ; Get exam ID from candidateData object

            ; Update status bar
            sbMain.SetText("Assigning seat for " candidateName "...")

            ; Call the seat assignment function from DBManager
            try {
                result := g_dbManager.AssignSeat(rollNumber, candidateName, examId)

                if (result.success) {
                    ; Parse the seatId (format: F1-R3-S12)
                    if (RegExMatch(result.seatId, "^F(\d+)-R(\d+)-S(\d+)$", &match)) {
                        floor := match[1]
                        room := match[2]
                        seat := match[3]

                        ; Make sure SeatDetailsText is properly initialized
                        if (IsObject(SeatDetailsText)) {
                            ; Update the seat details text with green color
                            SeatDetailsText.Text := "Seat: " seat ", Room: " room ", Floor: " floor
                            SeatDetailsText.Opt("cGreen")
                            SeatDetailsText.Visible := true
                            OutputDebug("Seat details text updated and made visible")
                        } else {
                            OutputDebug("Warning: SeatDetailsText control is not available")
                        }
                    } else {
                        OutputDebug("Warning: Could not parse seat ID format: " result.seatId)
                    }

                    ; Update the UI with the assigned seat information
                    AssignedSeatValue.Text := "Assigned"
                    AssignedSeatValue.Opt("cGreen")

                    ; Disable the assign seat button to prevent duplicate assignments
                    ButtonAssignSeat.Enabled := false

                    ; Show success message with allocation type
                    sbMain.SetText(result.message)

                    ; Log the assignment
                    OutputDebug("Seat assigned successfully: " result.seatId " for " rollNumber)

                    ; Show beautiful modal dialog with seat assignment information
                    ShowSeatAssignmentDialog(floor, room, seat, candidateData.Name)

                    ; Preserve the roll number search field value but clear the search status
                    ; EditRollSearch.Value := "" - No longer clearing to improve workflow efficiency
                    EditNameSearch.Value := ""

                    ; Hide seat details text immediately
                    SeatDetailsText.Visible := false

                    ; Reset candidate information fields immediately
                    CandidateNameText.Text := "-"
                    FatherNameText.Text := "-"
                    GenderText.Text := "-"
                    DateOfBirthText.Text := "-"
                    LanguageText.Text := "-"
                    SpecialStatusText.Text := "-"
                    RegisteredPhoto.Value := "img\default_photo.png"
                    RegisteredSignature.Value := "img\default_signature.png"

                    ; Reset verification statuses immediately
                    VerificationStatusValue.Text := "-"
                    VerificationStatusValue.Opt("cGray")
                    PostVerificationStatusValue.Text := "-"
                    PostVerificationStatusValue.Opt("cGray")
                    AssignedSeatValue.Text := "-"
                    AssignedSeatValue.Opt("cGray")

                    ; Disable capture and verification sections
                    CaptureGroupBox.Opt("+Disabled")
                    VerificationGroupBox.Opt("+Disabled")

                    ; Reset verification images
                    VerifyPhotoImage.Value := "img\default_photo.png"
                    VerifyFingerprintImage.Value := "img\default_fingerprint.png"
                    VerifyRightFingerprintImage.Value := "img\default_fingerprint.png"

                    ; Reset verification statuses
                    PhotoStatusValue.Text := "-"
                    PhotoStatusValue.Opt("cGray")
                    FingerprintStatusValue.Text := "-"
                    FingerprintStatusValue.Opt("cGray")
                    RightFingerprintStatusValue.Text := "-"
                    RightFingerprintStatusValue.Opt("cGray")

                    ; Reset verification buttons
                    ButtonVerifyPicture.Enabled := false
                    ButtonVerifyFingerprint.Enabled := false
                    ButtonVerifyRightFingerprint.Enabled := false
                    ButtonAssignSeat.Enabled := false

                    ; Reset webcam feed
                    WebcamFeed.Value := "img\default_photo.png"

                    ; Update status bar
                    sbMain.SetText("Ready for next candidate search")

                    ; Focus on the roll number search field
                    try {
                        EditRollSearch.Focus()
                        OutputDebug("Focus set to roll number search field")
                    } catch as err {
                        OutputDebug("Error setting focus to roll number search field: " err.Message)
                    }

                    ; Log that we're about to reset the interface
                    OutputDebug("Seat assignment complete - calling UniversalGUIReset()")

                    ; Reset the interface for next search directly
                    UniversalGUIReset("assignment", true)

                    ; Additional log after reset
                    OutputDebug("UniversalGUIReset() completed")
                } else {
                    ; Show error message
                    sbMain.SetText(result.message)

                    ; Get debug information
                    debugInfo := g_dbManager.DebugCacheStatus()

                    ; Show detailed error message with debug info
                    errorMsg := result.message "`n`nDebug Information:`n" debugInfo
                    MsgBox(errorMsg, "Seat Assignment Failed", "Icon!")
                    OutputDebug("Seat assignment failed: " result.message)

                    ; Force reload of caches and try again
                    g_dbManager.ReloadCache()
                    OutputDebug("Reloaded caches after failure")
                }
            } catch Error as e {
                errorMsg := "Error assigning seat: " e.Message
                sbMain.SetText(errorMsg)
                MsgBox(errorMsg, "Error", "Icon! 262208")
                OutputDebug(errorMsg)
            }
        }
    }

    ; Register event handlers
    EditRollSearch.OnEvent("Change", OnEventHandler)
    ButtonSearch.OnEvent("Click", OnEventHandler)
    ButtonCapturePicture.OnEvent("Click", OnEventHandler)
    ButtonUsePhoto.OnEvent("Click", OnEventHandler)
    ButtonRecapturePhoto.OnEvent("Click", OnEventHandler)
    ButtonCaptureFingerprint.OnEvent("Click", OnEventHandler)
    ButtonCaptureRightFingerprint.OnEvent("Click", OnEventHandler)
    EnableRightThumbCheckbox.OnEvent("Click", OnEventHandler)
    SingleThumbCheckbox.OnEvent("Click", OnEventHandler)
    LeftThumbRadio.OnEvent("Click", OnEventHandler)
    RightThumbRadio.OnEvent("Click", OnEventHandler)
    ButtonCaptureSignature.OnEvent("Click", OnEventHandler)
    ButtonVerifyPicture.OnEvent("Click", OnEventHandler)
    ButtonVerifyFingerprint.OnEvent("Click", OnEventHandler)
    ButtonVerifyRightFingerprint.OnEvent("Click", OnEventHandler)
    ButtonVerifySignature.OnEvent("Click", OnEventHandler)
    ButtonAssignSeat.OnEvent("Click", OnEventHandler)

    return myGui
}

; ; UpdateTitleBar()
; ; Updates the title bar text and colors based on the current post-exam mode status
; ; This ensures consistent title display throughout the application
UpdateTitleBar() {
    global TitleText, PostExamModeEnabled

    if (IsObject(TitleText)) {
        if (PostExamModeEnabled) {
            ; Post-Exam Mode: Blue background, White text
            TitleText.Text := "Exam Verification System [Post-Exam]"
            TitleText.Opt("cWhite +Background004080")
        } else {
            ; Pre-Exam Mode: Green background, Black text
            TitleText.Text := "Exam Verification System [Pre-Exam]"
            TitleText.Opt("cWhite +Background008000")
        }
        ; Force the control to redraw
        TitleText.Redraw()
    }
}

; ; ===== GUI RESET AND CLEANUP FUNCTIONS =====

; ; UniversalGUIReset()
; ; Master function for resetting the GUI interface to its initial state.
; ; Provides different reset modes for different scenarios.
; ; @param resetMode: "search" (after search), "assignment" (after seat assignment), "complete" (full reset)
; ; @param preserveRollNumber: Whether to preserve the roll number search field value
UniversalGUIReset(resetMode := "complete", preserveRollNumber := true) {
    OutputDebug("UniversalGUIReset called with mode: " resetMode ", preserveRollNumber: " preserveRollNumber)

    try {
        ; Reset based on mode
        switch resetMode {
            case "search":
                ResetForNewSearch(preserveRollNumber)
            case "assignment":
                ResetAfterSeatAssignment(preserveRollNumber)
            case "complete":
                ResetCompleteInterface(preserveRollNumber)
            default:
                OutputDebug("UniversalGUIReset: Unknown reset mode, using complete reset")
                ResetCompleteInterface(preserveRollNumber)
        }

        ; Common post-reset actions
        FinalizeReset()

        OutputDebug("UniversalGUIReset: Reset completed successfully for mode: " resetMode)

    } catch as err {
        OutputDebug("UniversalGUIReset: Error during reset: " err.Message)
        ErrorHandler.LogMessage("ERROR", "Error in UniversalGUIReset: " err.Message)
    }
}

; ; ResetForNewSearch()
; ; Resets interface when starting a new candidate search
; ; @param preserveRollNumber: Whether to preserve the roll number search field value
ResetForNewSearch(preserveRollNumber := true) {
    OutputDebug("ResetForNewSearch: Starting reset for new search")

    ; Reset search fields
    ResetSearchFields(preserveRollNumber)

    ; Reset capture section but keep camera active
    ResetCaptureSection(false)  ; false = don't stop camera

    ; Reset verification section
    ResetVerificationSection()

    ; Reset candidate information
    ResetCandidateInfo()

    ; Reset post-exam mode
    ResetPostExamMode()
}

; ; ResetAfterSeatAssignment()
; ; Resets interface after successful seat assignment
; ; @param preserveRollNumber: Whether to preserve the roll number search field value
ResetAfterSeatAssignment(preserveRollNumber := true) {
    OutputDebug("ResetAfterSeatAssignment: Starting reset after seat assignment")

    ; Reset search fields
    ResetSearchFields(preserveRollNumber)

    ; Completely disable capture section
    ResetCaptureSection(true)  ; true = stop camera

    ; Reset verification section
    ResetVerificationSection()

    ; Reset candidate information
    ResetCandidateInfo()

    ; Reset post-exam mode
    ResetPostExamMode()

    ; Hide seat details
    HideSeatDetails()
}

; ; ResetCompleteInterface()
; ; Performs a complete interface reset
; ; @param preserveRollNumber: Whether to preserve the roll number search field value
ResetCompleteInterface(preserveRollNumber := true) {
    OutputDebug("ResetCompleteInterface: Starting complete interface reset")

    ; Reset all sections
    ResetSearchFields(preserveRollNumber)
    ResetCaptureSection(false)  ; Keep camera active
    ResetVerificationSection()
    ResetCandidateInfo()
    ResetPostExamMode()
    HideSeatDetails()
}

; ; ResetSearchFields()
; ; Resets search-related fields and controls
; ; @param preserveRollNumber: Whether to preserve the roll number search field value
ResetSearchFields(preserveRollNumber := true) {
    OutputDebug("ResetSearchFields: Starting search fields reset")

    try {
        ; Explicitly declare all global variables needed in this function
        global EditRollSearch, EditNameSearch, ButtonSearch
        global CandidateNameText, FatherNameText, GenderText, DateOfBirthText, LanguageText, SpecialStatusText
        global RegisteredPhoto, RegisteredSignature, SignatureCaptureImage

        ; Handle roll number search field
        if (IsSet(EditRollSearch) && IsObject(EditRollSearch)) {
            if (!preserveRollNumber) {
                EditRollSearch.Value := ""
                OutputDebug("ResetSearchFields: Cleared roll number search field")
            } else {
                OutputDebug("ResetSearchFields: Preserved roll number search field value")
            }
        } else {
            OutputDebug("ResetSearchFields: EditRollSearch not available")
        }

        ; Reset name search field
        if (IsSet(EditNameSearch) && IsObject(EditNameSearch)) {
            EditNameSearch.Value := ""
            EditNameSearch.Opt("")  ; Reset text color to default
            OutputDebug("ResetSearchFields: Reset name search field")
        } else {
            OutputDebug("ResetSearchFields: EditNameSearch not available")
        }

        OutputDebug("ResetSearchFields: Search fields reset completed")
    } catch as err {
        OutputDebug("ResetSearchFields: Error during reset: " err.Message)
    }
}

; ; ResetCaptureSection()
; ; Resets capture section controls and data using existing disable/enable patterns
; ; @param stopCamera: Whether to stop the camera (true) or keep it running (false)
ResetCaptureSection(stopCamera := false) {
    OutputDebug("ResetCaptureSection: Starting capture section reset, stopCamera: " stopCamera)

    try {
        ; Disable capture group box using existing pattern
        global CaptureGroupBox
        if (IsSet(CaptureGroupBox) && IsObject(CaptureGroupBox)) {
            CaptureGroupBox.Opt("+Disabled")
            OutputDebug("ResetCaptureSection: Disabled capture group box")
        } else {
            OutputDebug("ResetCaptureSection: CaptureGroupBox not available")
        }

        ; Reset capture buttons using existing disable pattern
        DisableCaptureButtons()

        ; Reset captured data
        ResetCapturedData()

        ; Handle camera state
        ManageCameraState(stopCamera)

        ; Hide special controls using existing pattern
        DisableSpecialControls()

        OutputDebug("ResetCaptureSection: Capture section reset completed")
    } catch as err {
        OutputDebug("ResetCaptureSection: Error during reset: " err.Message)
    }
}

; ; DisableCaptureButtons()
; ; Disables all capture-related buttons following existing disable patterns
DisableCaptureButtons() {
    OutputDebug("DisableCaptureButtons: Starting capture buttons disable")

    try {
        global ButtonCapturePicture, ButtonCaptureFingerprint, ButtonCaptureRightFingerprint, ButtonCaptureSignature
        global ReviewPhotoLabel, ButtonUsePhoto, ButtonRecapturePhoto

        ; Disable photo capture controls
        if (IsSet(ButtonCapturePicture) && IsObject(ButtonCapturePicture)) {
            ButtonCapturePicture.Enabled := false
            ButtonCapturePicture.Text := "Capture Photo"
            ButtonCapturePicture.Visible := true
            OutputDebug("DisableCaptureButtons: Disabled photo capture button")
        } else {
            OutputDebug("DisableCaptureButtons: ButtonCapturePicture not available")
        }

        ; Hide review buttons completely
        if (IsSet(ReviewPhotoLabel) && IsObject(ReviewPhotoLabel)) {
            ReviewPhotoLabel.Visible := false
            OutputDebug("DisableCaptureButtons: Hidden ReviewPhotoLabel")
        }
        if (IsSet(ButtonUsePhoto) && IsObject(ButtonUsePhoto)) {
            ButtonUsePhoto.Visible := false
            OutputDebug("DisableCaptureButtons: Hidden ButtonUsePhoto")
        }
        if (IsSet(ButtonRecapturePhoto) && IsObject(ButtonRecapturePhoto)) {
            ButtonRecapturePhoto.Visible := false
            OutputDebug("DisableCaptureButtons: Hidden ButtonRecapturePhoto")
        }

        ; Disable fingerprint capture buttons
        if (IsSet(ButtonCaptureFingerprint) && IsObject(ButtonCaptureFingerprint)) {
            ButtonCaptureFingerprint.Enabled := false
            ButtonCaptureFingerprint.Text := "Capture ThumbPrint (Left)"
            OutputDebug("DisableCaptureButtons: Disabled left thumbprint capture button")
        } else {
            OutputDebug("DisableCaptureButtons: ButtonCaptureFingerprint not available")
        }

        if (IsSet(ButtonCaptureRightFingerprint) && IsObject(ButtonCaptureRightFingerprint)) {
            ButtonCaptureRightFingerprint.Enabled := false
            ButtonCaptureRightFingerprint.Text := "Capture ThumbPrint (Right)"
            OutputDebug("DisableCaptureButtons: Disabled right thumbprint capture button")
        } else {
            OutputDebug("DisableCaptureButtons: ButtonCaptureRightFingerprint not available")
        }

        ; Disable signature capture button
        if (IsSet(ButtonCaptureSignature) && IsObject(ButtonCaptureSignature)) {
            ButtonCaptureSignature.Enabled := false
            ButtonCaptureSignature.Text := "Capture Signature"
            OutputDebug("DisableCaptureButtons: Disabled signature capture button")
        } else {
            OutputDebug("DisableCaptureButtons: ButtonCaptureSignature not available")
        }

        ; Note: Verification buttons are disabled by ResetVerificationSection()
        ; Note: Special controls are disabled by DisableSpecialControls()

        OutputDebug("DisableCaptureButtons: Capture buttons disable completed")
    } catch as err {
        OutputDebug("DisableCaptureButtons: Error during disable: " err.Message)
    }
}

; ; ResetCapturedData()
; ; Resets all captured data paths and calls individual reset functions
ResetCapturedData() {
    OutputDebug("ResetCapturedData: Starting captured data reset")

    try {
        ; Reset captured paths
        global capturedPhotoPath, capturedFingerprintPath, capturedSignaturePath
        capturedPhotoPath := ""
        capturedFingerprintPath := ""
        capturedSignaturePath := ""

        ; Call individual reset functions for UI controls if they exist
        try {
            if (IsSet(ResetCapturedPhoto) && HasMethod(ResetCapturedPhoto))
                ResetCapturedPhoto()
        } catch {
            OutputDebug("ResetCapturedData: ResetCapturedPhoto function not available")
        }

        try {
            if (IsSet(ResetCapturedSignature) && HasMethod(ResetCapturedSignature))
                ResetCapturedSignature()
        } catch {
            OutputDebug("ResetCapturedData: ResetCapturedSignature function not available")
        }

        try {
            if (IsSet(ResetCapturedLeftThumbprint) && HasMethod(ResetCapturedLeftThumbprint))
                ResetCapturedLeftThumbprint()
        } catch {
            OutputDebug("ResetCapturedData: ResetCapturedLeftThumbprint function not available")
        }

        try {
            if (IsSet(ResetCapturedRightThumbprint) && HasMethod(ResetCapturedRightThumbprint))
                ResetCapturedRightThumbprint()
        } catch {
            OutputDebug("ResetCapturedData: ResetCapturedRightThumbprint function not available")
        }

        OutputDebug("ResetCapturedData: Captured data reset completed")
    } catch as err {
        OutputDebug("ResetCapturedData: Error during reset: " err.Message)
    }
}

; ; === CAMERA STATE MANAGEMENT ===

; ; InitializeCameraOnStartup()
; ; Performs one-time camera initialization and verification at application startup
; ; Keeps camera controls disabled initially, shows feed briefly to verify camera works, then hides it
InitializeCameraOnStartup() {
    OutputDebug("InitializeCameraOnStartup: Starting camera initialization and verification")

    try {
        global capHwnd, isWebcamActive, webcamControl, capturedImageControl, cameraName
        global g_cameraInitialized, g_cameraVerificationDone, g_cameraStatus

        ; Skip if already initialized
        if (g_cameraInitialized) {
            OutputDebug("InitializeCameraOnStartup: Camera already initialized, skipping")
            return true
        }

        ; Attempt to start camera for verification
        capHwnd := StartWebcam(webcamControl, cameraName)

        if (capHwnd) {
            isWebcamActive := true
            g_cameraStatus := cameraName ? cameraName : "Connected"
            g_cameraInitialized := true

            ; Show webcam feed briefly for verification
            ToggleWebcamAndCapturedImage(webcamControl, capturedImageControl, true)
            OutputDebug("InitializeCameraOnStartup: Camera started successfully, showing feed for verification")

            ; Update footer with camera status
            UpdateNetworkInfo()

            ; Wait 2-3 seconds to verify camera is working
            SetTimer(() => HideCameraAfterVerification(), -2500)  ; Hide after 2.5 seconds

            return true
        } else {
            isWebcamActive := false
            g_cameraStatus := "Not Connected"
            g_cameraInitialized := false
            OutputDebug("InitializeCameraOnStartup: Failed to initialize camera")

            ; Update footer with camera status
            UpdateNetworkInfo()

            return false
        }
    } catch as err {
        OutputDebug("InitializeCameraOnStartup: Error during camera initialization: " err.Message)
        g_cameraInitialized := false
        g_cameraStatus := "Error"
        return false
    }
}

; ; HideCameraAfterVerification()
; ; Hides camera feed after startup verification and disables camera controls
; ; Shows "Camera Detected" message when camera verification is successful
HideCameraAfterVerification() {
    OutputDebug("HideCameraAfterVerification: Hiding camera feed after startup verification")

    try {
        global g_cameraVerificationDone, ButtonCapturePicture, webcamControl, capturedImageControl
        global g_cameraInitialized, capHwnd, isWebcamActive

        ; If camera was successfully initialized, stop the live feed and show "Camera Detected" message
        if (g_cameraInitialized && IsObject(webcamControl) && IsObject(capturedImageControl)) {
            ; Stop the active camera feed first
            if (capHwnd && isWebcamActive) {
                StopWebcam(capHwnd)
                capHwnd := 0
                isWebcamActive := false
                OutputDebug("HideCameraAfterVerification: Stopped live camera feed")
            }

            ; Hide the captured image control
            HideControl(capturedImageControl)

            ; Show the webcam control with "Camera Detected" message
            ShowControl(webcamControl)

            ; Set "Camera Detected" message for successful verification
            if (webcamControl.HasProp("Text")) {
                webcamControl.Text := "Camera Detected"
            }

            OutputDebug("HideCameraAfterVerification: Camera verification successful - showing 'Camera Detected' message")
        } else {
            ; If camera initialization failed, use the standard inactive state
            SetCameraInactiveState()
            OutputDebug("HideCameraAfterVerification: Camera verification failed - showing standard inactive state")
        }

        ; Ensure camera controls remain disabled
        if (IsSet(ButtonCapturePicture) && IsObject(ButtonCapturePicture)) {
            ButtonCapturePicture.Enabled := false
            OutputDebug("HideCameraAfterVerification: Camera controls disabled")
        }

        g_cameraVerificationDone := true
        OutputDebug("HideCameraAfterVerification: Camera verification completed")
    } catch as err {
        OutputDebug("HideCameraAfterVerification: Error hiding camera: " err.Message)
    }
}

; ; EnableCameraForValidCandidate()
; ; Enables camera controls and shows live feed for valid candidates
; ; @param candidateStatus: The status of the candidate ("Active", "Not Active", etc.)
; ; @param seatStatus: Whether seat is already assigned
; ; @return: true if camera was enabled, false otherwise
EnableCameraForValidCandidate(candidateStatus, seatStatus := "") {
    OutputDebug("EnableCameraForValidCandidate: Checking if camera should be enabled for candidate")
    OutputDebug("EnableCameraForValidCandidate: Status=" candidateStatus ", SeatStatus=" seatStatus)

    try {
        global capHwnd, isWebcamActive, webcamControl, capturedImageControl, cameraName
        global g_cameraInitialized, g_validCandidateLoaded, ButtonCapturePicture

        ; Check if candidate is valid for camera access
        if (candidateStatus != "Active") {
            OutputDebug("EnableCameraForValidCandidate: Candidate not active, camera remains disabled")
            g_validCandidateLoaded := false

            ; Use "No Live Camera Feed" text instead of placeholder images for inactive candidates
            if (IsObject(webcamControl) && IsObject(capturedImageControl)) {
                ; Hide the captured image control
                HideControl(capturedImageControl)

                ; Show the webcam control with "No Live Camera Feed" message
                ShowControl(webcamControl)

                ; Set "No Live Camera Feed" message for inactive candidates
                if (webcamControl.HasProp("Text")) {
                    webcamControl.Text := "No Live Camera Feed"
                }

                OutputDebug("EnableCameraForValidCandidate: Camera feed set to 'No Live Camera Feed' for non-active candidate")
            }
            return false
        }

        if (seatStatus == "Seat already assigned") {
            OutputDebug("EnableCameraForValidCandidate: Seat already assigned, camera remains disabled")
            g_validCandidateLoaded := false

            ; Use "No Live Camera Feed" text instead of placeholder images for assigned candidates
            if (IsObject(webcamControl) && IsObject(capturedImageControl)) {
                ; Hide the captured image control
                HideControl(capturedImageControl)

                ; Show the webcam control with "No Live Camera Feed" message
                ShowControl(webcamControl)

                ; Set "No Live Camera Feed" message for assigned candidates
                if (webcamControl.HasProp("Text")) {
                    webcamControl.Text := "No Live Camera Feed"
                }

                OutputDebug("EnableCameraForValidCandidate: Camera feed set to 'No Live Camera Feed' for candidate with assigned seat")
            }
            return false
        }

        ; Camera should be enabled for this candidate
        g_validCandidateLoaded := true

        ; Initialize camera if not already done
        if (!g_cameraInitialized) {
            if (!InitializeCameraOnStartup()) {
                OutputDebug("EnableCameraForValidCandidate: Failed to initialize camera")
                return false
            }
        }

        ; Start camera and show live feed
        if (!capHwnd || !isWebcamActive) {
            capHwnd := StartWebcam(webcamControl, cameraName)
            if (capHwnd) {
                isWebcamActive := true
                OutputDebug("EnableCameraForValidCandidate: Camera started for valid candidate")
            } else {
                OutputDebug("EnableCameraForValidCandidate: Failed to start camera")
                return false
            }
        }

        ; Show webcam feed
        if (IsObject(webcamControl) && IsObject(capturedImageControl)) {
            ToggleWebcamAndCapturedImage(webcamControl, capturedImageControl, true)
            OutputDebug("EnableCameraForValidCandidate: Live camera feed enabled")
        }

        ; Enable camera controls
        if (IsSet(ButtonCapturePicture) && IsObject(ButtonCapturePicture)) {
            ButtonCapturePicture.Enabled := true
            OutputDebug("EnableCameraForValidCandidate: Camera controls enabled")
        }

        return true
    } catch as err {
        OutputDebug("EnableCameraForValidCandidate: Error enabling camera: " err.Message)
        return false
    }
}

; ; ManageCameraState()
; ; Manages camera state during resets
; ; @param stopCamera: Whether to stop the camera (true) or keep it running (false)
ManageCameraState(stopCamera := false) {
    OutputDebug("ManageCameraState: Managing camera state, stopCamera: " stopCamera)

    try {
        global capHwnd, webcamControl, capturedImageControl, cameraName, isWebcamActive
        global g_validCandidateLoaded

        if (stopCamera) {
            ; Stop the camera
            if (capHwnd && isWebcamActive) {
                StopWebcam(capHwnd)
                capHwnd := 0
                isWebcamActive := false
                OutputDebug("ManageCameraState: Camera stopped")
            }
            g_validCandidateLoaded := false
        } else {
            ; Only restart camera if we have a valid candidate loaded
            if (g_validCandidateLoaded) {
                ; Ensure camera is running and showing live feed
                if (!capHwnd || !isWebcamActive) {
                    ; Restart camera if not active
                    capHwnd := StartWebcam(webcamControl, cameraName)
                    if (capHwnd) {
                        isWebcamActive := true
                        OutputDebug("ManageCameraState: Camera restarted")
                    }
                }

                ; Show webcam feed instead of captured image
                if (IsObject(webcamControl) && IsObject(capturedImageControl)) {
                    ToggleWebcamAndCapturedImage(webcamControl, capturedImageControl, true)
                    OutputDebug("ManageCameraState: Switched to live webcam feed")
                }
            } else {
                OutputDebug("ManageCameraState: No valid candidate loaded, camera remains disabled")
            }
        }

        OutputDebug("ManageCameraState: Camera state management completed")
    } catch as err {
        OutputDebug("ManageCameraState: Error managing camera state: " err.Message)
    }
}

; ; === FINGERPRINT CAPTURE STATE MANAGEMENT ===

; ; ShouldLeftThumbBeEnabled()
; ; Determines if the left thumb capture button should be enabled based on current configuration
; ; @return: true if left thumb button should be enabled, false otherwise
ShouldLeftThumbBeEnabled() {
    try {
        global EnableRightThumbCheckbox, SingleThumbCheckbox, LeftThumbRadio, RightThumbRadio
        global RightThumbprintVerificationEnabled, g_specialAccommodation
        global FingerprintStatusValue

        ; PRIORITY 0: Check if fingerprint is already saved/verified - if so, disable button
        if (IsSet(FingerprintStatusValue) && IsObject(FingerprintStatusValue)) {
            if (FingerprintStatusValue.Text == "Saved" || FingerprintStatusValue.Text == "Verified") {
                OutputDebug("ShouldLeftThumbBeEnabled: Left fingerprint already " FingerprintStatusValue.Text " - button should be disabled")
                return false
            }
        }

        ; PRIORITY 1: Check special candidate accommodation first to prevent flickering
        if (g_specialAccommodation.isSpecialCandidate) {
            ; For special candidates, use their accommodation selection
            if (g_specialAccommodation.leftThumb && g_specialAccommodation.rightThumb) {
                ; Both thumbs selected - enable left if dual thumb mode is enabled
                return RightThumbprintVerificationEnabled
            } else if (g_specialAccommodation.leftThumb) {
                ; Left thumb only - always enable left
                return true
            } else if (g_specialAccommodation.rightThumb) {
                ; Right thumb only - disable left
                return false
            } else {
                ; No thumbs selected (shouldn't happen) - disable left
                return false
            }
        }

        ; PRIORITY 2: Standard logic for non-special candidates
        ; Check if we're in single thumb mode (no dual thumb support)
        if (!RightThumbprintVerificationEnabled) {
            ; In single thumb mode, left thumb is enabled unless right thumb is specifically selected
            if (IsSet(EnableRightThumbCheckbox) && IsObject(EnableRightThumbCheckbox)) {
                return !EnableRightThumbCheckbox.Value  ; Left enabled when right thumb checkbox is NOT checked
            }
            return true  ; Default to left thumb enabled in single thumb mode
        }

        ; In dual thumb mode, check the single thumb checkbox and radio buttons
        if (IsSet(SingleThumbCheckbox) && IsObject(SingleThumbCheckbox) && SingleThumbCheckbox.Value) {
            ; Single thumb mode is active, check which radio button is selected
            if (IsSet(LeftThumbRadio) && IsObject(LeftThumbRadio)) {
                return LeftThumbRadio.Value  ; Left enabled only if left radio is selected
            }
            return false  ; Default to disabled if radio buttons not available
        }

        ; In dual thumb mode with single thumb checkbox unchecked, both thumbs should be enabled
        return true
    } catch as err {
        OutputDebug("ShouldLeftThumbBeEnabled: Error determining state: " err.Message)
        return true  ; Default to enabled on error
    }
}

; ; ShouldRightThumbBeEnabled()
; ; Determines if the right thumb capture button should be enabled based on current configuration
; ; @return: true if right thumb button should be enabled, false otherwise
ShouldRightThumbBeEnabled() {
    try {
        global EnableRightThumbCheckbox, SingleThumbCheckbox, LeftThumbRadio, RightThumbRadio
        global RightThumbprintVerificationEnabled, g_specialAccommodation
        global RightFingerprintStatusValue

        ; PRIORITY 0: Check if right fingerprint is already saved/verified - if so, disable button
        if (IsSet(RightFingerprintStatusValue) && IsObject(RightFingerprintStatusValue)) {
            if (RightFingerprintStatusValue.Text == "Saved" || RightFingerprintStatusValue.Text == "Verified") {
                OutputDebug("ShouldRightThumbBeEnabled: Right fingerprint already " RightFingerprintStatusValue.Text " - button should be disabled")
                return false
            }
        }

        ; PRIORITY 1: Check special candidate accommodation first to prevent flickering
        if (g_specialAccommodation.isSpecialCandidate) {
            ; For special candidates, use their accommodation selection
            if (g_specialAccommodation.leftThumb && g_specialAccommodation.rightThumb) {
                ; Both thumbs selected - enable right if dual thumb mode is enabled
                return RightThumbprintVerificationEnabled
            } else if (g_specialAccommodation.rightThumb) {
                ; Right thumb only - always enable right
                return true
            } else if (g_specialAccommodation.leftThumb) {
                ; Left thumb only - disable right
                return false
            } else {
                ; No thumbs selected (shouldn't happen) - disable right
                return false
            }
        }

        ; PRIORITY 2: Standard logic for non-special candidates
        ; Check if we're in single thumb mode (no dual thumb support)
        if (!RightThumbprintVerificationEnabled) {
            ; In single thumb mode, right thumb is enabled only when specifically selected
            if (IsSet(EnableRightThumbCheckbox) && IsObject(EnableRightThumbCheckbox)) {
                return EnableRightThumbCheckbox.Value  ; Right enabled when right thumb checkbox is checked
            }
            return false  ; Default to disabled in single thumb mode
        }

        ; In dual thumb mode, check the single thumb checkbox and radio buttons
        if (IsSet(SingleThumbCheckbox) && IsObject(SingleThumbCheckbox) && SingleThumbCheckbox.Value) {
            ; Single thumb mode is active, check which radio button is selected
            if (IsSet(RightThumbRadio) && IsObject(RightThumbRadio)) {
                return RightThumbRadio.Value  ; Right enabled only if right radio is selected
            }
            return false  ; Default to disabled if radio buttons not available
        }

        ; In dual thumb mode with single thumb checkbox unchecked, both thumbs should be enabled
        return true
    } catch as err {
        OutputDebug("ShouldRightThumbBeEnabled: Error determining state: " err.Message)
        return false  ; Default to disabled on error for right thumb
    }
}

; ; SetFingerprintCaptureState()
; ; Sets the capture state for fingerprint buttons and provides visual feedback
; ; @param isLeftCapturing: Whether left fingerprint capture is in progress
; ; @param isRightCapturing: Whether right fingerprint capture is in progress
SetFingerprintCaptureState(isLeftCapturing := false, isRightCapturing := false) {
    OutputDebug("SetFingerprintCaptureState: Left=" isLeftCapturing ", Right=" isRightCapturing)

    try {
        global ButtonCaptureFingerprint, ButtonCaptureRightFingerprint
        global g_leftFingerprintCapturing, g_rightFingerprintCapturing
        global EnableRightThumbCheckbox, SingleThumbCheckbox, LeftThumbRadio, RightThumbRadio
        global RightThumbprintVerificationEnabled

        ; Update global state flags
        g_leftFingerprintCapturing := isLeftCapturing
        g_rightFingerprintCapturing := isRightCapturing

        ; Handle left fingerprint button
        if (IsSet(ButtonCaptureFingerprint) && IsObject(ButtonCaptureFingerprint)) {
            if (isLeftCapturing) {
                ButtonCaptureFingerprint.Enabled := false
                ButtonCaptureFingerprint.Text := "Capturing..."
                OutputDebug("SetFingerprintCaptureState: Left fingerprint button disabled, showing 'Capturing...'")
            } else {
                ; Always restore original text first when capture completes
                ButtonCaptureFingerprint.Text := "Capture ThumbPrint (Left)"

                ; Then check if button should be enabled based on configuration and verification status
                shouldEnable := ShouldLeftThumbBeEnabled()
                if (!g_rightFingerprintCapturing && shouldEnable) {
                    ButtonCaptureFingerprint.Enabled := true
                    OutputDebug("SetFingerprintCaptureState: Left fingerprint button re-enabled with original text" . (g_specialAccommodation.isSpecialCandidate ? " (special candidate logic applied)" : ""))
                } else {
                    ButtonCaptureFingerprint.Enabled := false
                    OutputDebug("SetFingerprintCaptureState: Left fingerprint button disabled with original text based on configuration" . (g_specialAccommodation.isSpecialCandidate ? " (special candidate logic applied)" : ""))
                }
            }
        }

        ; Handle right fingerprint button
        if (IsSet(ButtonCaptureRightFingerprint) && IsObject(ButtonCaptureRightFingerprint)) {
            if (isRightCapturing) {
                ButtonCaptureRightFingerprint.Enabled := false
                ButtonCaptureRightFingerprint.Text := "Capturing..."
                OutputDebug("SetFingerprintCaptureState: Right fingerprint button disabled, showing 'Capturing...'")
            } else {
                ; Always restore original text first when capture completes
                ButtonCaptureRightFingerprint.Text := "Capture ThumbPrint (Right)"

                ; Then check if button should be enabled based on configuration and verification status
                shouldEnable := ShouldRightThumbBeEnabled()
                if (!g_leftFingerprintCapturing && shouldEnable) {
                    ButtonCaptureRightFingerprint.Enabled := true
                    OutputDebug("SetFingerprintCaptureState: Right fingerprint button re-enabled with original text" . (g_specialAccommodation.isSpecialCandidate ? " (special candidate logic applied)" : ""))
                } else {
                    ButtonCaptureRightFingerprint.Enabled := false
                    OutputDebug("SetFingerprintCaptureState: Right fingerprint button disabled with original text based on configuration" . (g_specialAccommodation.isSpecialCandidate ? " (special candidate logic applied)" : ""))
                }
            }
        }

        OutputDebug("SetFingerprintCaptureState: Fingerprint capture state updated")
    } catch as err {
        OutputDebug("SetFingerprintCaptureState: Error setting capture state: " err.Message)
    }
}

; ; StartFingerprintCapture()
; ; Initiates fingerprint capture and sets appropriate button states
; ; @param isLeftThumb: Whether this is for left thumb (true) or right thumb (false)
; ; @return: true if capture state was set successfully
StartFingerprintCapture(isLeftThumb := true) {
    OutputDebug("StartFingerprintCapture: Starting capture for " (isLeftThumb ? "left" : "right") " thumb")

    try {
        global sbMain

        ; Check if any capture is already in progress
        if (g_leftFingerprintCapturing || g_rightFingerprintCapturing) {
            OutputDebug("StartFingerprintCapture: Another fingerprint capture already in progress")
            if (IsSet(sbMain) && IsObject(sbMain)) {
                try {
                    sbMain.Text := "Please wait, fingerprint capture in progress..."
                } catch as err {
                    OutputDebug("StartFingerprintCapture: Error updating status bar: " err.Message)
                }
            }
            return false
        }

        ; Set the appropriate capture state
        if (isLeftThumb) {
            SetFingerprintCaptureState(true, false)
            if (IsSet(sbMain) && IsObject(sbMain)) {
                try {
                    sbMain.Text := "Place your left thumb on the scanner..."
                } catch as err {
                    OutputDebug("StartFingerprintCapture: Error updating status bar: " err.Message)
                }
            }
        } else {
            SetFingerprintCaptureState(false, true)
            if (IsSet(sbMain) && IsObject(sbMain)) {
                try {
                    sbMain.Text := "Place your right thumb on the scanner..."
                } catch as err {
                    OutputDebug("StartFingerprintCapture: Error updating status bar: " err.Message)
                }
            }
        }

        OutputDebug("StartFingerprintCapture: Capture state set for " (isLeftThumb ? "left" : "right") " thumb")
        return true
    } catch as err {
        OutputDebug("StartFingerprintCapture: Error starting capture: " err.Message)
        return false
    }
}

; ; CompleteFingerprintCapture()
; ; Completes fingerprint capture and restores button states
; ; @param isLeftThumb: Whether this was for left thumb (true) or right thumb (false)
; ; @param success: Whether the capture was successful
; ; @param message: Optional status message to display
CompleteFingerprintCapture(isLeftThumb := true, success := true, message := "") {
    OutputDebug("CompleteFingerprintCapture: Completing capture for " (isLeftThumb ? "left" : "right") " thumb, success=" success)

    try {
        global sbMain, g_leftFingerprintCapturing, g_rightFingerprintCapturing

        ; Clear the capture state variables first to ensure proper state reset
        if (isLeftThumb) {
            g_leftFingerprintCapturing := false
            SetFingerprintCaptureState(false, g_rightFingerprintCapturing)
        } else {
            g_rightFingerprintCapturing := false
            SetFingerprintCaptureState(g_leftFingerprintCapturing, false)
        }

        ; For error scenarios (timeout, device errors), ensure buttons are properly restored
        if (!success) {
            ; Force button state restoration for error recovery
            if (isLeftThumb) {
                if (IsSet(ButtonCaptureFingerprint) && IsObject(ButtonCaptureFingerprint)) {
                    ; Check if left thumb should be enabled based on current configuration
                    shouldEnable := ShouldLeftThumbBeEnabled()
                    if (shouldEnable) {
                        ButtonCaptureFingerprint.Enabled := true
                        ButtonCaptureFingerprint.Text := "Capture ThumbPrint (Left)"
                        OutputDebug("CompleteFingerprintCapture: Error recovery - Left fingerprint button restored")
                    }
                }
            } else {
                if (IsSet(ButtonCaptureRightFingerprint) && IsObject(ButtonCaptureRightFingerprint)) {
                    ; Check if right thumb should be enabled based on current configuration
                    shouldEnable := ShouldRightThumbBeEnabled()
                    if (shouldEnable) {
                        ButtonCaptureRightFingerprint.Enabled := true
                        ButtonCaptureRightFingerprint.Text := "Capture ThumbPrint (Right)"
                        OutputDebug("CompleteFingerprintCapture: Error recovery - Right fingerprint button restored")
                    }
                }
            }
        }

        ; Update status bar with result
        if (IsSet(sbMain) && IsObject(sbMain)) {
            try {
                if (message != "") {
                    sbMain.Text := message
                } else if (success) {
                    sbMain.Text := (isLeftThumb ? "Left" : "Right") " fingerprint captured successfully"
                } else {
                    sbMain.Text := (isLeftThumb ? "Left" : "Right") " fingerprint capture failed"
                }
            } catch as err {
                OutputDebug("CompleteFingerprintCapture: Error updating status bar: " err.Message)
            }
        }

        ; Note: Special candidate button state management is now handled automatically
        ; by the ShouldLeftThumbBeEnabled() and ShouldRightThumbBeEnabled() functions
        ; in SetFingerprintCaptureState(), preventing button flickering

        OutputDebug("CompleteFingerprintCapture: Capture completed for " (isLeftThumb ? "left" : "right") " thumb")
    } catch as err {
        OutputDebug("CompleteFingerprintCapture: Error completing capture: " err.Message)
    }
}

; ; DisableSpecialControls()
; ; Disables special candidate controls (thumbprint selection options) following existing disable patterns
DisableSpecialControls() {
    OutputDebug("DisableSpecialControls: Starting special controls disable")

    try {
        global EnableRightThumbCheckbox, SingleThumbCheckbox, LeftThumbRadio, RightThumbRadio, SpecialCaseIndicator

        ; Disable and reset special case controls
        if (IsSet(EnableRightThumbCheckbox) && IsObject(EnableRightThumbCheckbox)) {
            EnableRightThumbCheckbox.Value := false
            EnableRightThumbCheckbox.Enabled := false
            EnableRightThumbCheckbox.Visible := false
        } else {
            OutputDebug("DisableSpecialControls: EnableRightThumbCheckbox not available")
        }

        if (IsSet(SingleThumbCheckbox) && IsObject(SingleThumbCheckbox)) {
            SingleThumbCheckbox.Value := false
            SingleThumbCheckbox.Enabled := false
            SingleThumbCheckbox.Visible := false
        } else {
            OutputDebug("DisableSpecialControls: SingleThumbCheckbox not available")
        }

        if (IsSet(LeftThumbRadio) && IsObject(LeftThumbRadio)) {
            LeftThumbRadio.Value := false
            LeftThumbRadio.Enabled := false
            LeftThumbRadio.Visible := false
        } else {
            OutputDebug("DisableSpecialControls: LeftThumbRadio not available")
        }

        if (IsSet(RightThumbRadio) && IsObject(RightThumbRadio)) {
            RightThumbRadio.Value := false
            RightThumbRadio.Enabled := false
            RightThumbRadio.Visible := false
        } else {
            OutputDebug("DisableSpecialControls: RightThumbRadio not available")
        }

        if (IsSet(SpecialCaseIndicator) && IsObject(SpecialCaseIndicator)) {
            SpecialCaseIndicator.Visible := false
        } else {
            OutputDebug("DisableSpecialControls: SpecialCaseIndicator not available")
        }

        OutputDebug("DisableSpecialControls: Special controls disabled")
    } catch as err {
        OutputDebug("DisableSpecialControls: Error disabling special controls: " err.Message)
    }
}

; ; ResetVerificationSection()
; ; Resets verification section controls and status
ResetVerificationSection() {
    OutputDebug("ResetVerificationSection: Starting verification section reset")

    try {
        global VerificationGroupBox, VerifyPhotoImage, VerifyFingerprintImage, VerifyRightFingerprintImage
        global SignatureVerifyImage, SignatureCaptureImage, PhotoStatusValue, FingerprintStatusValue
        global RightFingerprintStatusValue, SignatureStatusValue, VerificationStatusValue
        global ButtonVerifyPicture, ButtonVerifyFingerprint, ButtonVerifyRightFingerprint, ButtonVerifySignature
        global ButtonAssignSeat, AssignedSeatValue, PostVerificationStatusValue
        global SignatureVerificationEnabled, FingerprintMode

        ; Disable verification group box
        if (IsSet(VerificationGroupBox) && IsObject(VerificationGroupBox)) {
            VerificationGroupBox.Opt("+Disabled")
            OutputDebug("ResetVerificationSection: Disabled verification group box")
        } else {
            OutputDebug("ResetVerificationSection: VerificationGroupBox not available")
        }

        ; Reset verification images
        if (IsSet(VerifyPhotoImage) && IsObject(VerifyPhotoImage))
            VerifyPhotoImage.Value := "img\default_photo.png"
        if (IsSet(VerifyFingerprintImage) && IsObject(VerifyFingerprintImage))
            VerifyFingerprintImage.Value := "img\default_fingerprint.png"
        if (IsSet(VerifyRightFingerprintImage) && IsObject(VerifyRightFingerprintImage))
            VerifyRightFingerprintImage.Value := "img\default_fingerprint.png"

        ; Handle signature verification based on configuration
        if (IsSet(SignatureVerifyImage) && IsObject(SignatureVerifyImage)) {
            if (SignatureVerificationEnabled) {
                SignatureVerifyImage.Value := "img\default_signature.png"
                SignatureVerifyImage.Opt("-Disabled")
            } else {
                SignatureVerifyImage.Value := "img\gray.png"
                SignatureVerifyImage.Opt("+Disabled")
            }
        }

        if (IsSet(SignatureCaptureImage) && IsObject(SignatureCaptureImage)) {
            if (SignatureVerificationEnabled) {
                SignatureCaptureImage.Value := "img\default_signature.png"
            } else {
                SignatureCaptureImage.Value := "img\gray.png"
            }
        }

        ; Reset verification status values
        if (IsSet(PhotoStatusValue) && IsObject(PhotoStatusValue)) {
            PhotoStatusValue.Text := "-"
            PhotoStatusValue.Opt("cGray")
        }

        if (IsSet(FingerprintStatusValue) && IsObject(FingerprintStatusValue)) {
            FingerprintStatusValue.Text := "-"
            FingerprintStatusValue.Opt("cGray")
        }

        if (IsSet(RightFingerprintStatusValue) && IsObject(RightFingerprintStatusValue)) {
            RightFingerprintStatusValue.Text := "-"
            RightFingerprintStatusValue.Opt("cGray")
        }

        ; Handle signature status based on configuration
        if (IsSet(SignatureStatusValue) && IsObject(SignatureStatusValue)) {
            if (!SignatureVerificationEnabled) {
                SignatureStatusValue.Text := "Disabled"
            } else {
                SignatureStatusValue.Text := "-"
            }
            SignatureStatusValue.Opt("cGray")
        }

        if (IsSet(VerificationStatusValue) && IsObject(VerificationStatusValue)) {
            VerificationStatusValue.Text := "-"
            VerificationStatusValue.Opt("cGray")
        }

        if (IsSet(PostVerificationStatusValue) && IsObject(PostVerificationStatusValue)) {
            PostVerificationStatusValue.Text := "-"
            PostVerificationStatusValue.Opt("cGray")
        }

        if (IsSet(AssignedSeatValue) && IsObject(AssignedSeatValue)) {
            AssignedSeatValue.Text := "-"
            AssignedSeatValue.Opt("cGray")
        }

        ; Reset verification buttons
        if (IsSet(ButtonVerifyPicture) && IsObject(ButtonVerifyPicture)) {
            ButtonVerifyPicture.Enabled := false
            ButtonVerifyPicture.Text := "Verify Photo"
        }

        if (IsSet(ButtonVerifyFingerprint) && IsObject(ButtonVerifyFingerprint)) {
            ButtonVerifyFingerprint.Enabled := false
            if (FingerprintMode == "save") {
                ButtonVerifyFingerprint.Text := "Save ThumbPrint (Left)"
            } else {
                ButtonVerifyFingerprint.Text := "Verify ThumbPrint (Left)"
            }
        }

        if (IsSet(ButtonVerifyRightFingerprint) && IsObject(ButtonVerifyRightFingerprint)) {
            ButtonVerifyRightFingerprint.Enabled := false
            if (FingerprintMode == "save") {
                ButtonVerifyRightFingerprint.Text := "Save ThumbPrint (Right)"
            } else {
                ButtonVerifyRightFingerprint.Text := "Verify ThumbPrint (Right)"
            }
        }

        if (IsSet(ButtonVerifySignature) && IsObject(ButtonVerifySignature)) {
            ButtonVerifySignature.Enabled := false
            if (!SignatureVerificationEnabled) {
                ButtonVerifySignature.Text := "Disabled"
            } else {
                ButtonVerifySignature.Text := "Verify Signature"
            }
        }

        ; Reset seat assignment button
        if (IsSet(ButtonAssignSeat) && IsObject(ButtonAssignSeat)) {
            ButtonAssignSeat.Enabled := false
            ButtonAssignSeat.Visible := true
        }

        OutputDebug("ResetVerificationSection: Verification section reset completed")
    } catch as err {
        OutputDebug("ResetVerificationSection: Error during reset: " err.Message)
    }
}

; ; ResetCandidateInfo()
; ; Resets candidate information display fields
ResetCandidateInfo() {
    OutputDebug("ResetCandidateInfo: Starting candidate info reset")

    try {
        global CandidateNameText, FatherNameText, GenderText, DateOfBirthText
        global LanguageText, SpecialStatusText, RegisteredPhoto, RegisteredSignature

        ; Reset all candidate information fields
        if (IsSet(CandidateNameText) && IsObject(CandidateNameText))
            CandidateNameText.Text := "-"
        if (IsSet(FatherNameText) && IsObject(FatherNameText))
            FatherNameText.Text := "-"
        if (IsSet(GenderText) && IsObject(GenderText))
            GenderText.Text := "-"
        if (IsSet(DateOfBirthText) && IsObject(DateOfBirthText))
            DateOfBirthText.Text := "-"
        if (IsSet(LanguageText) && IsObject(LanguageText))
            LanguageText.Text := "-"
        if (IsSet(SpecialStatusText) && IsObject(SpecialStatusText))
            SpecialStatusText.Text := "-"
        if (IsSet(RegisteredPhoto) && IsObject(RegisteredPhoto))
            RegisteredPhoto.Value := "img\default_photo.png"
        if (IsSet(RegisteredSignature) && IsObject(RegisteredSignature))
            RegisteredSignature.Value := "img\default_signature.png"

        OutputDebug("ResetCandidateInfo: Candidate info reset completed")
    } catch as err {
        OutputDebug("ResetCandidateInfo: Error during reset: " err.Message)
    }
}

; ; ResetPostExamMode()
; ; Resets post-exam mode flags and updates title bar
ResetPostExamMode() {
    OutputDebug("ResetPostExamMode: Starting post-exam mode reset")

    try {
        ; Reset post-exam mode flags
        global g_isPostExamMode, g_invalidPostExam
        g_isPostExamMode := false
        g_invalidPostExam := false

        ; Reset special accommodation selection
        global g_specialAccommodation
        g_specialAccommodation := {leftThumb: false, rightThumb: false, isSpecialCandidate: false}
        OutputDebug("ResetPostExamMode: Reset special accommodation selection")

        ; Update title bar to reflect current mode
        UpdateTitleBar()

        OutputDebug("ResetPostExamMode: Post-exam mode reset completed")
    } catch as err {
        OutputDebug("ResetPostExamMode: Error during reset: " err.Message)
    }
}

; ; HideSeatDetails()
; ; Hides seat details text and clears its content
HideSeatDetails() {
    OutputDebug("HideSeatDetails: Starting seat details hide")

    try {
        global SeatDetailsText

        ; Always ensure seat details text is hidden when resetting interface
        if (IsObject(SeatDetailsText)) {
            SeatDetailsText.Visible := false
            SeatDetailsText.Text := ""  ; Clear the text as well
            OutputDebug("HideSeatDetails: Seat details hidden and cleared")
        }

        OutputDebug("HideSeatDetails: Seat details hide completed")
    } catch as err {
        OutputDebug("HideSeatDetails: Error during hide: " err.Message)
    }
}

; ; FinalizeReset()
; ; Performs final actions after reset (focus, status bar update)
FinalizeReset() {
    OutputDebug("FinalizeReset: Starting finalization")

    try {
        global EditRollSearch, sbMain

        ; Update status bar
        if (IsSet(sbMain) && IsObject(sbMain)) {
            sbMain.SetText("Ready for next candidate search")
            OutputDebug("FinalizeReset: Updated status bar")
        } else {
            OutputDebug("FinalizeReset: Status bar not available")
        }

        ; Focus on the roll number search field to prepare for next search
        if (IsSet(EditRollSearch) && IsObject(EditRollSearch)) {
            try {
                ; Just set focus without selecting text
                EditRollSearch.Focus()

                ; Move cursor to end of text without selecting
                SendInput("{End}")

                OutputDebug("FinalizeReset: Focus set to roll number search field without selection")
            } catch as err {
                OutputDebug("FinalizeReset: Error setting focus to roll number search field: " err.Message)
            }
        } else {
            OutputDebug("FinalizeReset: Roll number search field not available")
        }

        OutputDebug("FinalizeReset: Finalization completed")
    } catch as err {
        OutputDebug("FinalizeReset: Error during finalization: " err.Message)
    }
}



; ; ShowGeneralNotificationDialog(...)
; ; Shows a general-purpose modal notification dialog with distinctive styling
; ; Features purple/lavender theme to distinguish from other dialogs
; ; @param title: The dialog title
; ; @param message: The main message text
; ; @param buttonText: The button text (default: "OK")
; ; @param iconText: Optional icon/emoji for the header (default: "ℹ")
; ; @return: true when dialog is closed
; ;
; ; Usage Examples:
; ; ShowGeneralNotificationDialog("Information", "Operation completed successfully!")
; ; ShowGeneralNotificationDialog("Warning", "Please check your settings.", "Understood", "⚠")
; ; ShowGeneralNotificationDialog("Error", "An error occurred during processing.", "Close", "❌")
ShowGeneralNotificationDialog(title, message, buttonText := "OK", iconText := "ℹ") {
    global myGui

    ; Disable the main GUI while notification dialog is open
    myGui.Opt("+Disabled")

    ; Create a distinctive notification dialog with purple/violet theme
    notificationDialog := Gui("+AlwaysOnTop +Border +Owner" myGui.Hwnd " -MinimizeBox -MaximizeBox -SysMenu", title)

    ; Set distinctive purple background
    notificationDialog.BackColor := 0xE6E6FA  ; Lavender background
    notificationDialog.SetFont("s11 Bold", "Segoe UI")

    ; Add distinctive header with purple theme
    headerText := notificationDialog.Add("Text", "x10 y10 w400 h35 Center +Border +Background8A2BE2 cWhite", iconText . " " . title . " " . iconText)
    headerText.SetFont("s14 Bold")

    ; Add message text with dark text on light background
    notificationDialog.SetFont("s10", "Segoe UI")
    messageControl := notificationDialog.Add("Text", "x20 y55 w380 h60 cBlack +Wrap", message)

    ; Add styled button with purple theme
    okButton := notificationDialog.Add("Button", "x160 y125 w100 h40 +0x1000 Default", buttonText)
    okButton.SetFont("s12 Bold")
    okButton.Opt("cPurple")

    ; Event handlers
    okButton.OnEvent("Click", CloseNotificationDialog)
    notificationDialog.OnEvent("Close", CloseNotificationDialog)

    ; Define close handler
    CloseNotificationDialog(*) {
        ; Re-enable the main GUI
        myGui.Opt("-Disabled")

        ; Close the dialog
        notificationDialog.Destroy()
    }

    ; Show the dialog with appropriate size
    notificationDialog.Show("w420 h180")

    ; Make dialog modal - wait for it to close
    WinWaitClose(title)

    return true
}

; ; ShowSeatAssignmentDialog(...)
; ; Shows a beautiful modal dialog for successful seat assignment with visual appeal
; ; @param floor: The floor number
; ; @param room: The room number
; ; @param seat: The seat number
; ; @param candidateName: The name of the candidate
ShowSeatAssignmentDialog(floor, room, seat, candidateName) {
    global myGui

    ; Disable the main GUI while seat assignment dialog is open
    myGui.Opt("+Disabled")

    ; Create a colorful seat assignment dialog
    seatDialog := Gui("-AlwaysOnTop +Border +Owner" myGui.Hwnd " -MinimizeBox -MaximizeBox -SysMenu", "Seat Assignment Successful")

    ; Set bright green background for success
    seatDialog.BackColor := 0x90EE90  ; Light green background
    seatDialog.SetFont("s11 Bold", "Segoe UI")

    ; Add success header with contrasting colors
    headerText := seatDialog.Add("Text", "x10 y10 w400 h35 Center +Border +Background008000 cWhite", "🎯 SEAT ASSIGNED SUCCESSFULLY! 🎯")
    headerText.SetFont("s14 Bold")

    ; Add candidate name section
    seatDialog.SetFont("s11", "Segoe UI")
    candidateText := seatDialog.Add("Text", "x20 y55 w380 h25 +Border +BackgroundF0F8FF cNavy", "Candidate: " candidateName)
    candidateText.SetFont("s11 Bold")

    ; Add seat details with colorful styling
    seatDialog.SetFont("s12 Bold", "Segoe UI")

    ; Floor info
    floorText := seatDialog.Add("Text", "x50 y90 w300 h30 cBlue", "🏢 Floor: " floor)

    ; Room info
    roomText := seatDialog.Add("Text", "x50 y120 w300 h30 cGreen", "🚪 Room: " room)

    ; Seat info
    seatText := seatDialog.Add("Text", "x50 y150 w300 h30 cRed", "💺 Seat: " seat)

    ; Add instruction text
    seatDialog.SetFont("s10", "Segoe UI")
    instructionText := seatDialog.Add("Text", "x20 y190 w380 h25 Center cBlack", "Please guide the candidate to their assigned seat.")
    instructionText.SetFont("s10 Italic")

    ; Add styled OK button
    okButton := seatDialog.Add("Button", "x160 y225 w100 h40 +0x1000 Default", "✓ OK")
    okButton.SetFont("s12 Bold")
    okButton.Opt("cGreen")

    ; Event handlers
    okButton.OnEvent("Click", CloseSeatDialog)
    seatDialog.OnEvent("Close", CloseSeatDialog)

    ; Define close handler
    CloseSeatDialog(*) {
        ; Re-enable the main GUI
        myGui.Opt("-Disabled")

        ; Close the dialog
        seatDialog.Destroy()
    }

    ; Show the dialog with appropriate size
    seatDialog.Show("w420 h280")

    ; Make dialog modal - wait for it to close
    WinWaitClose("Seat Assignment Successful")
}

; ; ShowSpecialAccommodationDialog(...)
; ; Shows a dialog for candidates with Special=1 flag to select which thumb impression is required.
; ; @param rollNumber: The roll number of the candidate.
; ; @param defaultPreference: Optional - The default thumb preference to select ("Both", "Left", "Right")
; ; @param singleThumbMode: Optional - Whether the application is in single thumb mode (no dual thumb support)
; ; @return: An object containing the selected option: {leftThumb: bool, rightThumb: bool}.
ShowSpecialAccommodationDialog(rollNumber, defaultPreference := "Both", singleThumbMode := false) {
    global RightThumbprintVerificationEnabled, myGui

    ; Disable the main GUI while special dialog is open
    myGui.Opt("+Disabled")

    ; Create a colorful special accommodation dialog with enhanced styling
    specialDialog := Gui("-AlwaysOnTop +Border +Owner" myGui.Hwnd " -MinimizeBox -MaximizeBox -SysMenu", "Thumbprint Selection for Special Candidate")

    ; Set attractive background color
    specialDialog.BackColor := 0xF0F8FF  ; Alice blue background
    specialDialog.SetFont("s12 Bold", "Segoe UI")

    ; Add colorful header
    headerText := specialDialog.Add("Text", "x10 y10 w460 h35 Center +Border +Background4169E1 cWhite", "🔒 SPECIAL CANDIDATE ACCOMMODATION 🔒")
    headerText.SetFont("s14 Bold")

    ; Add message text with dark text on bright background
    specialDialog.SetFont("s10", "Segoe UI")
    if (singleThumbMode) {
        messageText := specialDialog.Add("Text", "x20 y50 w420 h40 cBlack", "This candidate has a special status flag.`nPlease select which thumb will be used for verification:")
    } else {
        messageText := specialDialog.Add("Text", "x20 y50 w420 h40 cBlack", "This candidate has a special status flag.`nPlease select which thumb impression will be required:")
    }

    ; Initialize variables
    bothThumbs := ""
    leftThumbOnly := ""
    rightThumbOnly := ""

    ; Add radio buttons based on mode
    if (singleThumbMode) {
        ; In single thumb mode, only show Left and Right options
        leftThumbOnly := specialDialog.Add("Radio", "x20 y100 w200 Group Checked", "Left Thumb Only")
        rightThumbOnly := specialDialog.Add("Radio", "x20 y130 w200", "Right Thumb Only")

        ; Default to Left Thumb in single thumb mode
        result := {leftThumb: true, rightThumb: false}

        ; Override with stored preference if available
        if (defaultPreference = "Right") {
            leftThumbOnly.Value := false
            rightThumbOnly.Value := true
            result := {leftThumb: false, rightThumb: true}
        }
    } else {
        ; In dual thumb mode, show all three options
        bothThumbs := specialDialog.Add("Radio", "x20 y100 w200 Group", "Both Thumbs (if possible)")
        leftThumbOnly := specialDialog.Add("Radio", "x20 y130 w200", "Left Thumb Only")
        rightThumbOnly := specialDialog.Add("Radio", "x20 y160 w200", "Right Thumb Only")

        ; Set the default selection based on the defaultPreference parameter
        if (defaultPreference = "Left") {
            leftThumbOnly.Value := true
            result := {leftThumb: true, rightThumb: false}
        } else if (defaultPreference = "Right") {
            rightThumbOnly.Value := true
            result := {leftThumb: false, rightThumb: true}
        } else {
            ; Default to "Both" for any other value
            bothThumbs.Value := true
            result := {leftThumb: true, rightThumb: true}
        }
    }

    ; Calculate dialog height and button position based on mode
    if (singleThumbMode) {
        dialogHeight := 250
        buttonY := 190
    } else {
        dialogHeight := 280
        buttonY := 220
    }

    ; Add styled confirm button
    okButton := specialDialog.Add("Button", "x190 y" buttonY " w100 h40 +0x1000 Default", "✓ Confirm")
    okButton.SetFont("s12 Bold")
    okButton.Opt("cBlue")
    okButton.OnEvent("Click", OkButtonClick)

    ; Define OK button click handler
    OkButtonClick(*) {
        ; Determine the selected preference
        thumbPreference := "Both"

        if (leftThumbOnly.Value) {
            result.leftThumb := true
            result.rightThumb := false
            thumbPreference := "Left"
            OutputDebug("Special candidate thumbprint selection: Left Thumb Only selected for candidate " rollNumber)
        }
        else if (rightThumbOnly.Value) {
            result.leftThumb := false
            result.rightThumb := true
            thumbPreference := "Right"
            OutputDebug("Special candidate thumbprint selection: Right Thumb Only selected for candidate " rollNumber)
        }
        else if (bothThumbs && bothThumbs.Value) {
            ; Only check bothThumbs if it exists (not in single thumb mode)
            result.leftThumb := true
            result.rightThumb := true
            thumbPreference := "Both"
            OutputDebug("Special candidate thumbprint selection: Both Thumbs selected for candidate " rollNumber)
        }

        ; Save the preference to candidates.ini
        try {
            ; Get the database path
            global dbPath

            ; Convert relative to absolute path if needed
            if (SubStr(dbPath, 1, 1) != "\" && SubStr(dbPath, 2, 1) != ":") {
                dbPathFull := A_ScriptDir "\" dbPath
            } else {
                dbPathFull := dbPath
            }

            ; Define database file path
            candidatesPath := dbPathFull "\candidates.ini"

            ; Save the thumb preference
            IniWrite(thumbPreference, candidatesPath, rollNumber, "ThumbPreference")
            OutputDebug("Saved thumb preference '" thumbPreference "' for candidate " rollNumber)
        } catch Error as e {
            OutputDebug("Error saving thumb preference: " e.Message)
        }

        ; Log the selection
        logMessage := FormatTime(, "yyyy-MM-dd HH:mm:ss") " - Special candidate thumbprint selection for candidate " rollNumber ": "
        if (result.leftThumb && result.rightThumb)
            logMessage .= "Both Thumbs"
        else if (result.leftThumb)
            logMessage .= "Left Thumb Only"
        else if (result.rightThumb)
            logMessage .= "Right Thumb Only"

        FileAppend(logMessage "`n", "logs\application.log")

        ; Re-enable the main GUI
        myGui.Opt("-Disabled")

        ; Close the dialog
        specialDialog.Destroy()
    }

    ; Show the dialog with appropriate size
    specialDialog.Show("w480 h" dialogHeight)

    ; Make dialog modal - wait for it to close
    WinWaitClose("Thumbprint Selection for Special Candidate")

    return result
}

; ; SaveThumbPreferenceForNonSpecialCandidate(...)
; ; Saves the thumb preference for non-special candidates based on current control states.
; ; This function is called when thumb selection controls change in pre-exam mode.
; ; @param rollNumber: The roll number of the candidate.
SaveThumbPreferenceForNonSpecialCandidate(rollNumber) {
    try {
        ; Get the database path
        global dbPath

        ; Convert relative to absolute path if needed
        if (SubStr(dbPath, 1, 1) != "\" && SubStr(dbPath, 2, 1) != ":") {
            dbPathFull := A_ScriptDir "\" dbPath
        } else {
            dbPathFull := dbPath
        }

        ; Define database file path
        candidatesPath := dbPathFull "\candidates.ini"

        ; Determine thumb preference based on current control states
        global RightThumbprintVerificationEnabled, SingleThumbCheckbox, LeftThumbRadio, RightThumbRadio, EnableRightThumbCheckbox
        thumbPreference := "Both"  ; Default

        if (RightThumbprintVerificationEnabled) {
            ; DualThumb mode: Check SingleThumbCheckbox and radio buttons
            if (IsSet(SingleThumbCheckbox) && IsObject(SingleThumbCheckbox) && SingleThumbCheckbox.Value) {
                ; Single thumb mode is selected
                if (IsSet(LeftThumbRadio) && IsObject(LeftThumbRadio) && LeftThumbRadio.Value) {
                    thumbPreference := "Left"
                } else if (IsSet(RightThumbRadio) && IsObject(RightThumbRadio) && RightThumbRadio.Value) {
                    thumbPreference := "Right"
                } else {
                    thumbPreference := "Left"  ; Default to left if no radio button is selected
                }
            } else {
                ; Both thumbs mode
                thumbPreference := "Both"
            }
        } else {
            ; SingleThumb mode: Check EnableRightThumbCheckbox
            if (IsSet(EnableRightThumbCheckbox) && IsObject(EnableRightThumbCheckbox) && EnableRightThumbCheckbox.Value) {
                thumbPreference := "Right"
            } else {
                thumbPreference := "Left"
            }
        }

        ; Save the thumb preference
        IniWrite(thumbPreference, candidatesPath, rollNumber, "ThumbPreference")
        OutputDebug("Saved thumb preference '" thumbPreference "' for non-special candidate " rollNumber)

    } catch Error as e {
        OutputDebug("Error saving thumb preference for non-special candidate: " e.Message)
    }
}

; ; LoadCandidateData(...)
; ; Loads candidate information and biometric file paths from the candidates.ini database.
; ; Constructs absolute paths for biometric images based on the configured dbPath.
; ; Implements case-insensitive search for roll numbers.
; ; @param rollNumber: The roll number of the candidate to load.
; ; @return: An object containing the candidate's data (Name, Picture path, Signature path, etc.),
; ;         or an empty object if the candidate is not found or the database doesn't exist.
LoadCandidateData(rollNumber) {
    ; Access the global dbPath variable
    global dbPath

    ; Convert relative to absolute path if needed
    if (SubStr(dbPath, 1, 1) != "\" && SubStr(dbPath, 2, 1) != ":") {
        dbPath := A_ScriptDir "\" dbPath
    }

    ; Get database file paths using PathManager
    candidatesPath := PathManager.GetDatabaseFilePath("Candidates")
    candidatesImgPath := PathManager.GetDatabaseSubPath("CandidateImages")

    ; Check if the database file exists
    if (!FileExist(candidatesPath)) {
        MsgBox("Candidates database not found at " candidatesPath)
        return {}
    }

    ; Implement case-insensitive search
    ; First try direct lookup with the provided roll number
    candidateData := {}
    candidateData.Name := IniRead(candidatesPath, rollNumber, "Name", "")

    ; If not found, try case-insensitive search
    if (candidateData.Name == "") {
        ; Read all sections from the INI file
        try {
            ; In AutoHotkey v2, we need to read the file and parse it manually to get all sections
            fileContent := FileRead(candidatesPath)
            sections := []

            ; Parse the file to extract section names
            Loop Parse, fileContent, "`n", "`r" {
                if (RegExMatch(A_LoopField, "^\[(.*)\]$", &match)) {
                    sections.Push(match[1])
                }
            }

            if (sections.Length > 0) {
                ; Loop through all sections to find a case-insensitive match
                for index, section in sections {
                    if (section == rollNumber || StrLower(section) == StrLower(rollNumber)) {
                        ; Found a match, use this section instead
                        rollNumber := section
                        OutputDebug("Case-insensitive match found: " rollNumber)
                        break
                    }
                }
            }
        } catch Error as e {
            OutputDebug("Error during case-insensitive search: " e.Message)
        }
    }

    ; Now read the data with the potentially updated roll number
    candidateData.Name := IniRead(candidatesPath, rollNumber, "Name", "")
    candidateData.Email := IniRead(candidatesPath, rollNumber, "Email", "")
    candidateData.Mobile := IniRead(candidatesPath, rollNumber, "Mobile", "")

    ; Add Father Name and Date of Birth fields
    candidateData.FatherName := IniRead(candidatesPath, rollNumber, "FatherName", "")
    candidateData.DateOfBirth := IniRead(candidatesPath, rollNumber, "DateOfBirth", "")

    ; Construct paths to candidate biometric images using the new path structure
    ; For photo files
    candidateData.Picture := PathManager.GetCandidatePhotoPath(rollNumber)

    ; Check if we're in post-exam mode using the global variable
    global PostExamModeEnabled
    examPhase := PostExamModeEnabled ? "post" : "pre"

    ; Set captured paths based on exam phase
    candidateData.Captured := candidatesImgPath rollNumber "_captured_photo_" examPhase ".jpg"
    candidateData.Signature := candidatesImgPath rollNumber "_registered_signature.jpg"
    candidateData.CapturedSignature := candidatesImgPath rollNumber "_captured_signature_" examPhase ".jpg"

    ; Set fingerprint paths using PathManager
    candidateData.LeftFingerprintTemplate := PathManager.GetFingerprintTemplatePath(rollNumber, "left", examPhase)
    candidateData.LeftFingerprintImage := PathManager.GetFingerprintImagePath(rollNumber, "left", examPhase)
    candidateData.RightFingerprintTemplate := PathManager.GetFingerprintTemplatePath(rollNumber, "right", examPhase)
    candidateData.RightFingerprintImage := PathManager.GetFingerprintImagePath(rollNumber, "right", examPhase)

    candidateData.Gender := IniRead(candidatesPath, rollNumber, "Gender", "")
    candidateData.CenterID := IniRead(candidatesPath, rollNumber, "CenterID", "")
    candidateData.ExamID := IniRead(candidatesPath, rollNumber, "ExamID", "")
    candidateData.ComputerID := IniRead(candidatesPath, rollNumber, "ComputerID", "")
    candidateData.StudentID := IniRead(candidatesPath, rollNumber, "StudentID", "")
    candidateData.Status := IniRead(candidatesPath, rollNumber, "Status", "")
    candidateData.PreferredLanguage := IniRead(candidatesPath, rollNumber, "PreferredLanguage", "")
    candidateData.BiometricStatus := IniRead(candidatesPath, rollNumber, "BiometricStatus", "")

    ; Load verification status fields
    candidateData.PhotoStatus := IniRead(candidatesPath, rollNumber, "PhotoStatus", "")
    candidateData.FingerprintStatus := IniRead(candidatesPath, rollNumber, "FingerprintStatus", "")
    candidateData.RightFingerprintStatus := IniRead(candidatesPath, rollNumber, "RightFingerprintStatus", "")
    candidateData.SignatureStatus := IniRead(candidatesPath, rollNumber, "SignatureStatus", "")

    ; Load post-exam verification status fields
    candidateData.PostExamBiometricStatus := IniRead(candidatesPath, rollNumber, "PostExamBiometricStatus", "")
    candidateData.PostExamPhotoStatus := IniRead(candidatesPath, rollNumber, "PostExamPhotoStatus", "")
    candidateData.PostExamFingerprintStatus := IniRead(candidatesPath, rollNumber, "PostExamFingerprintStatus", "")
    candidateData.PostExamRightFingerprintStatus := IniRead(candidatesPath, rollNumber, "PostExamRightFingerprintStatus", "")
    candidateData.PostExamSignatureStatus := IniRead(candidatesPath, rollNumber, "PostExamSignatureStatus", "")

    ; Only check for the correctly spelled "Special" field
    candidateData.Special := IniRead(candidatesPath, rollNumber, "Special", "0")

    ; Load thumb preference for all candidates (used in post-exam mode to maintain consistency)
    candidateData.ThumbPreference := IniRead(candidatesPath, rollNumber, "ThumbPreference", "Both")

    ; Debug output
    OutputDebug("Loaded candidate data for Roll #" . rollNumber . ": Name=" . candidateData.Name . ", Special=" . candidateData.Special)
    OutputDebug("Candidate thumb preference: " . candidateData.ThumbPreference . " (Special: " . (candidateData.Special == "1" ? "Yes" : "No") . ")")
    OutputDebug("Verification status - Biometric: " . candidateData.BiometricStatus . ", Photo: " . candidateData.PhotoStatus .
                ", Left Fingerprint: " . candidateData.FingerprintStatus . ", Right Fingerprint: " . candidateData.RightFingerprintStatus .
                ", Signature: " . candidateData.SignatureStatus)
    OutputDebug("Post-Exam verification status - Biometric: " . candidateData.PostExamBiometricStatus . ", Photo: " . candidateData.PostExamPhotoStatus .
                ", Left Fingerprint: " . candidateData.PostExamFingerprintStatus . ", Right Fingerprint: " . candidateData.PostExamRightFingerprintStatus .
                ", Signature: " . candidateData.PostExamSignatureStatus)

    return candidateData
}








